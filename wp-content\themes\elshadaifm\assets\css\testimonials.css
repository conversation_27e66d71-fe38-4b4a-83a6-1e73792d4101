/* ===================================================================
   TESTIMONIALS SECTION - Clean & Minimal Design
   =================================================================== */

.testimonials-section {
    padding: 4rem 0 5rem;
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

/* Container */
.testimonials-section .container {
    width: 90%;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Main Wrapper */
.testimonials-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3rem;
}

/* ===================================================================
   TESTIMONIAL DISPLAY
   =================================================================== */

.testimonial-display {
    width: 100%;
    max-width: 800px;
    position: relative;
    min-height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.testimonial-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.testimonial-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(20px);
}

.testimonial-item.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Testimonial Content */
.testimonial-content {
    text-align: center;
    padding: 2rem 1rem;
}

/* Quote Icon */
.quote-icon {
    color: #e0e7ff;
    margin-bottom: 1.5rem;
    transform: rotate(180deg);
}

.quote-icon svg {
    width: 40px;
    height: 32px;
}

/* Testimonial Text */
.testimonial-text {
    font-size: 1.25rem;
    line-height: 1.7;
    color: #374151;
    font-weight: 400;
    margin: 0 0 2rem 0;
    font-style: italic;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Author Info */
.testimonial-author {
    display: flex;
    justify-content: center;
    align-items: center;
}

.author-info {
    text-align: center;
}

.author-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #111827;
    margin: 0 0 0.25rem 0;
}

.author-location {
    font-size: 0.95rem;
    color: #6b7280;
    margin: 0;
    opacity: 0.8;
}

/* ===================================================================
   PROGRESS INDICATORS
   =================================================================== */

.testimonial-indicators {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
}

.indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: none;
    background: #e5e7eb;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.indicator.active {
    background: #3b82f6;
    transform: scale(1.2);
}

.indicator:hover {
    background: #9ca3af;
    transform: scale(1.1);
}

.indicator:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* ===================================================================
   CTA BUTTON
   =================================================================== */

.testimonials-cta {
    margin-top: 1rem;
}

.view-testimonials-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: transparent;
    color: #374151;
    text-decoration: none;
    font-size: 1rem;
    font-weight: 500;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.view-testimonials-btn:hover {
    color: #3b82f6;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.view-testimonials-btn svg {
    transition: transform 0.3s ease;
}

.view-testimonials-btn:hover svg {
    transform: translateX(3px);
}

.view-testimonials-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */

@media (max-width: 768px) {
    .testimonials-section {
        padding: 3rem 0 4rem;
    }
    
    .testimonials-section .container {
        padding: 0 1.5rem;
    }
    
    .testimonials-wrapper {
        gap: 2rem;
    }
    
    .testimonial-display {
        min-height: 320px;
    }
    
    .testimonial-content {
        padding: 1.5rem 0.5rem;
    }
    
    .testimonial-text {
        font-size: 1.1rem;
        line-height: 1.6;
    }
    
    .quote-icon svg {
        width: 32px;
        height: 26px;
    }
    
    .author-name {
        font-size: 1rem;
    }
    
    .author-location {
        font-size: 0.9rem;
    }
    
    .view-testimonials-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .testimonials-section {
        padding: 2.5rem 0 3.5rem;
    }
    
    .testimonial-display {
        min-height: 280px;
    }
    
    .testimonial-text {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .testimonial-content {
        padding: 1rem 0;
    }
    
    .quote-icon {
        margin-bottom: 1rem;
    }
    
    .view-testimonials-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* ===================================================================
   ANIMATIONS & TRANSITIONS
   =================================================================== */

/* Smooth entrance animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Auto-load animation for first testimonial */
.testimonials-section .testimonial-item.active {
    animation: fadeInUp 0.8s ease-out;
}

/* Hover effects for better interactivity */
.testimonial-content:hover .quote-icon {
    color: #c7d2fe;
    transform: rotate(180deg) scale(1.05);
    transition: all 0.3s ease;
}

/* Focus states for accessibility */
.testimonials-section *:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* ===================================================================
   PREFERS REDUCED MOTION
   =================================================================== */

@media (prefers-reduced-motion: reduce) {
    .testimonial-item {
        transition: opacity 0.3s ease;
        transform: none;
    }
    
    .testimonial-item.active {
        animation: none;
    }
    
    .view-testimonials-btn:hover {
        transform: none;
    }
    
    .indicator:hover,
    .indicator.active {
        transform: none;
    }
}