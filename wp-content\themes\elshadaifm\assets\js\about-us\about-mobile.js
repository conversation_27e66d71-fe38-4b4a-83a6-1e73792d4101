/* ==========================================================================
   ABOUT US - MOBILE FUNCTIONALITY
   ========================================================================== */

/**
 * About Us Mobile Module
 * Handles mobile-specific functionality and responsive behavior
 */
window.AboutUsMobile = (function() {
    'use strict';

    // Private variables
    let isInitialized = false;
    let resizeTimeout = null;

    // Public API
    const api = {
        init: init,
        destroy: destroy,
        handleResize: handleResize
    };

    /**
     * Initialize mobile functionality
     */
    function init() {
        if (isInitialized) return;

        console.log('AboutUsMobile: Initializing...');

        // Setup mobile timeline handling
        handleMobileTimeline();

        // Setup resize handling
        setupResizeHandling();

        // Setup mobile-specific interactions
        setupMobileInteractions();

        isInitialized = true;
        console.log('AboutUsMobile: Initialized successfully');
    }

    /**
     * Destroy mobile functionality
     */
    function destroy() {
        if (resizeTimeout) {
            clearTimeout(resizeTimeout);
            resizeTimeout = null;
        }
        isInitialized = false;
    }

    /**
     * Handle mobile timeline functionality
     */
    function handleMobileTimeline() {
        const isMobile = window.innerWidth <= 768;
        const timeline = document.querySelector('.history-timeline');
        const timelineItems = document.querySelectorAll('.timeline-item');

        if (timeline && timelineItems.length > 0) {
            if (isMobile) {
                // Mobile-specific adjustments
                timeline.classList.add('mobile-timeline');

                // Adjust timeline items for mobile
                timelineItems.forEach((item, index) => {
                    item.style.animationDelay = `${index * 0.2}s`;

                    // Reset any desktop transforms
                    item.style.transform = 'translateY(30px)';
                    item.style.opacity = '0';
                });

                // Re-observe items for mobile animation
                const mobileObserver = new IntersectionObserver(function(entries) {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.style.opacity = '1';
                            entry.target.style.transform = 'translateY(0)';
                            entry.target.classList.add('visible');
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -20px 0px'
                });

                timelineItems.forEach(item => mobileObserver.observe(item));

            } else {
                // Desktop handling
                timeline.classList.remove('mobile-timeline');
            }
        }
    }

    /**
     * Setup resize handling
     */
    function setupResizeHandling() {
        window.addEventListener('resize', handleResize);
    }

    /**
     * Handle window resize
     */
    function handleResize() {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(() => {
            handleMobileTimeline();
            adjustMobileLayout();
        }, 250);
    }

    /**
     * Adjust mobile layout
     */
    function adjustMobileLayout() {
        const isMobile = window.innerWidth <= 768;

        // Adjust grid layouts for mobile
        const grids = document.querySelectorAll('.about-highlights, .values-grid, .stats-grid');
        grids.forEach(grid => {
            if (isMobile) {
                grid.style.gridTemplateColumns = '1fr';
            } else {
                grid.style.gridTemplateColumns = '';
            }
        });

        // Adjust CTA layout
        const ctaContent = document.querySelector('.cta-content');
        if (ctaContent) {
            if (isMobile) {
                ctaContent.style.gridTemplateColumns = '1fr';
                ctaContent.style.textAlign = 'center';
            } else {
                ctaContent.style.gridTemplateColumns = '';
                ctaContent.style.textAlign = '';
            }
        }
    }

    /**
     * Setup mobile-specific interactions
     */
    function setupMobileInteractions() {
        if (!AboutUsBase.isMobile()) return;

        // Add touch feedback
        setupTouchFeedback();

        // Setup swipe gestures (if needed)
        setupSwipeGestures();

        // Setup mobile navigation
        setupMobileNavigation();
    }

    /**
     * Setup touch feedback for mobile
     */
    function setupTouchFeedback() {
        const touchElements = document.querySelectorAll(
            '.timeline-item, .highlight-card, .value-card, .cta-button'
        );

        touchElements.forEach(element => {
            element.addEventListener('touchstart', function(e) {
                this.classList.add('touch-active');
            }, { passive: true });

            element.addEventListener('touchend', function(e) {
                setTimeout(() => {
                    this.classList.remove('touch-active');
                }, 150);
            }, { passive: true });

            element.addEventListener('touchcancel', function(e) {
                this.classList.remove('touch-active');
            }, { passive: true });
        });
    }

    /**
     * Setup swipe gestures
     */
    function setupSwipeGestures() {
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;

        const timeline = document.querySelector('.history-timeline');
        if (!timeline) return;

        timeline.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        }, { passive: true });

        timeline.addEventListener('touchmove', function(e) {
            // Prevent default scrolling if swiping horizontally
            const deltaX = Math.abs(e.touches[0].clientX - startX);
            const deltaY = Math.abs(e.touches[0].clientY - startY);

            if (deltaX > deltaY) {
                e.preventDefault();
            }
        });

        timeline.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;

            handleSwipe();
        }, { passive: true });

        function handleSwipe() {
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const minSwipeDistance = 50;

            // Only handle horizontal swipes
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
                if (deltaX > 0) {
                    // Swipe right - could navigate to previous timeline item
                    console.log('Swipe right detected');
                } else {
                    // Swipe left - could navigate to next timeline item
                    console.log('Swipe left detected');
                }
            }
        }
    }

    /**
     * Setup mobile navigation
     */
    function setupMobileNavigation() {
        // Add mobile-friendly section navigation
        const sections = document.querySelectorAll('section[id]');
        
        sections.forEach(section => {
            section.addEventListener('click', function(e) {
                // Only handle clicks on section headers for mobile navigation
                if (e.target.matches('.section-title, .about-title')) {
                    const nextSection = this.nextElementSibling;
                    if (nextSection && nextSection.tagName === 'SECTION') {
                        nextSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                }
            });
        });
    }

    /**
     * Handle orientation change
     */
    function handleOrientationChange() {
        // Wait for orientation change to complete
        setTimeout(() => {
            handleMobileTimeline();
            adjustMobileLayout();
        }, 500);
    }

    /**
     * Setup mobile viewport handling
     */
    function setupViewportHandling() {
        // Handle viewport changes on mobile browsers
        let viewportHeight = window.innerHeight;

        window.addEventListener('resize', () => {
            const currentHeight = window.innerHeight;
            const heightDifference = Math.abs(currentHeight - viewportHeight);

            // If height changed significantly (likely keyboard show/hide)
            if (heightDifference > 150) {
                document.body.classList.toggle('keyboard-visible', currentHeight < viewportHeight);
                viewportHeight = currentHeight;
            }
        });
    }

    // Setup additional mobile features
    setTimeout(() => {
        window.addEventListener('orientationchange', handleOrientationChange);
        setupViewportHandling();
    }, 100);

    return api;
})();
