<?php
/**
 * Admin Interface for Elshadaifm Renungan API
 */

class Elshadaifm_Renungan_Admin {

    private $auth;
    private $config;
    private $logger;
    private $helper;

    public function __construct() {
        $this->config = new Elshadaifm_Renungan_Config();
        $this->logger = new Elshadaifm_Renungan_Logger();
        $this->helper = new Elshadaifm_Renungan_Helper();

        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
    }

    public function add_admin_menu() {
        add_menu_page(
            'Renungan API Settings',
            'Renungan API',
            'manage_options',
            'elshadaifm-renungan-api',
            array($this, 'admin_page'),
            'dashicons-admin-generic',
            30
        );
    }

    public function register_settings() {
        register_setting('elshadaifm_renungan_api_settings', 'elshadaifm_api_key');
        register_setting('elshadaifm_renungan_api_settings', 'elshadaifm_openai_key');

        // Initialize default options
        Elshadaifm_Renungan_Config::initialize_options();
    }

    // API key generation moved to Elshadaifm_Renungan_Auth module

    public function admin_page() {
        $this->render_admin_page();
    }

    private function render_admin_page() {
        ?>
        <div class="wrap">
            <h1>Elshadaifm Renungan API Settings</h1>

            <?php $this->handle_form_submissions(); ?>

            <?php $this->render_api_key_card(); ?>
            <?php $this->render_openai_key_card(); ?>
            <?php $this->render_generate_key_card(); ?>
            <?php $this->render_documentation_card(); ?>
        </div>

        <?php $this->render_admin_scripts(); ?>
        <?php
    }

    private function handle_form_submissions() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            return;
        }

        if (isset($_POST['action']) && $_POST['action'] === 'generate_new_key') {
            $this->handle_generate_new_key();
        }

        if (isset($_POST['action']) && $_POST['action'] === 'save_openai_key') {
            $this->handle_save_openai_key();
        }
    }

    private function handle_generate_new_key() {
        try {
            Elshadaifm_Renungan_Auth::check_capabilities();
            Elshadaifm_Renungan_Auth::verify_nonce($_POST['api_key_nonce'], 'generate_new_api_key');

            $new_key = Elshadaifm_Renungan_Auth::generate_api_key();
            $this->config->set_api_key($new_key);

            $this->logger->log('New API key generated', Elshadaifm_Renungan_Logger::INFO);
            echo '<div class="notice notice-success is-dismissible"><p>New API key generated successfully!</p></div>';
        } catch (Exception $e) {
            $this->logger->log_error('Failed to generate new API key: ' . $e->getMessage());
            echo '<div class="notice notice-error is-dismissible"><p>Failed to generate new API key.</p></div>';
        }
    }

    private function handle_save_openai_key() {
        try {
            Elshadaifm_Renungan_Auth::check_capabilities();
            Elshadaifm_Renungan_Auth::verify_nonce($_POST['openai_key_nonce'], 'save_openai_key');

            $openai_key = Elshadaifm_Renungan_Auth::sanitize_api_key($_POST['openai_key']);
            $this->config->set_openai_key($openai_key);

            $this->logger->log('OpenAI API key saved', Elshadaifm_Renungan_Logger::INFO);
            echo '<div class="notice notice-success is-dismissible"><p>OpenAI API key saved successfully!</p></div>';
        } catch (Exception $e) {
            $this->logger->log_error('Failed to save OpenAI API key: ' . $e->getMessage());
            echo '<div class="notice notice-error is-dismissible"><p>Failed to save OpenAI API key.</p></div>';
        }
    }

    private function render_api_key_card() {
        ?>
        <div class="card">
            <h2>API Key</h2>
            <p>Gunakan API key ini untuk mengakses endpoint Renungan API:</p>

            <table class="form-table">
                <tr>
                    <th scope="row">API Key:</th>
                    <td>
                        <code style="font-size: 14px; padding: 5px 10px; background: #f0f0f0; border-radius: 3px;">
                            <?php echo esc_html($this->config->get_api_key()); ?>
                        </code>
                        <button type="button" class="button" onclick="copyApiKey()">Copy</button>
                    </td>
                </tr>
            </table>

            <p class="description">
                <strong>Cara penggunaan:</strong><br>
                Method: POST<br>
                URL: <?php echo rest_url('elshadaifm-renungan/v1/submit'); ?><br>
                <strong>Option 1 - Form Data:</strong><br>
                Header: <code>Content-Type: application/x-www-form-urlencoded</code><br>
                Body: <code>content=text renungan...&api_key=YOUR_API_KEY</code><br>
                <strong>Option 2 - Bearer Token:</strong><br>
                Header: <code>Authorization: Bearer YOUR_API_KEY</code><br>
                Header: <code>Content-Type: application/json</code><br>
                Body: <code>{"content": "text renungan..."}</code>
            </p>
        </div>
        <?php
    }

    private function render_openai_key_card() {
        ?>
        <div class="card">
            <h2>OpenAI API Key</h2>
            <p>API key untuk ekstraksi konten menggunakan GPT-4o-mini dan image generation menggunakan GPT Image-1:</p>

            <form method="post" action="">
                <?php wp_nonce_field('save_openai_key', 'openai_key_nonce'); ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">OpenAI API Key:</th>
                        <td>
                            <input type="text" name="openai_key" value="<?php echo esc_html($this->config->get_openai_key()); ?>" class="regular-text" style="width: 400px;">
                        </td>
                    </tr>
                </table>
                <input type="hidden" name="action" value="save_openai_key">
                <button type="submit" class="button button-primary">Save OpenAI Key</button>
            </form>
        </div>
        <?php
    }

    private function render_generate_key_card() {
        ?>
        <div class="card">
            <h2>Generate New API Key</h2>
            <p>Jika API key Anda terganggu, generate yang baru:</p>
            <form method="post" action="">
                <?php wp_nonce_field('generate_new_api_key', 'api_key_nonce'); ?>
                <input type="hidden" name="action" value="generate_new_key">
                <button type="submit" class="button button-primary">Generate New API Key</button>
            </form>
        </div>
        <?php
    }

    private function render_documentation_card() {
        ?>
        <div class="card">
            <h2>API Documentation</h2>
            <h3>Available Endpoints</h3>
            <ul>
                <?php foreach ($this->config->get_api_endpoints() as $route => $endpoint): ?>
                    <li><strong><?php echo ucfirst(str_replace('-', ' ', $route)); ?>:</strong> <code><?php echo $endpoint['method'] . ' ' . $endpoint['url']; ?></code></li>
                <?php endforeach; ?>
            </ul>

            <h3>Authentication</h3>
            <ul>
                <li><code>Authorization: Bearer YOUR_API_KEY</code></li>
                <li>Atau form data: <code>api_key=YOUR_API_KEY</code></li>
                <li>Current API Key: <code><?php echo substr($this->config->get_api_key(), 0, 10); ?>...</code></li>
                <li>OpenAI Status: <?php echo Elshadaifm_Renungan_Auth::validate_openai_access()['success'] ? '✅ Configured' : '❌ Not Configured'; ?></li>
            </ul>

            <h3>Workflow</h3>
            <ol>
                <li>Submit content → <code>/submit</code></li>
                <li>Extract details → <code>/generate-details</code></li>
                <li>Generate content → <code>/generate-content</code></li>
                <li>Generate image → <code>/generate-image</code></li>
            </ol>
        </div>
        <?php
    }

    private function render_admin_scripts() {
        ?>
        <script>
        function copyApiKey() {
            const apiKeyElement = document.querySelector('.card code');
            if (apiKeyElement) {
                const apiKey = apiKeyElement.textContent;
                navigator.clipboard.writeText(apiKey).then(() => {
                    alert('API key copied to clipboard!');
                });
            }
        }
        </script>

        <style>
        .card {
            background: #fff;
            border: 1px solid #ccd0d4;
            border-radius: 4px;
            margin: 20px 0;
            padding: 20px;
        }
        .card h2 {
            margin-top: 0;
            color: #23282d;
        }
        .card h3 {
            color: #444;
        }
        .card pre {
            background: #f7f7f7;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 3px;
            overflow-x: auto;
        }
        .card code {
            background: #f7f7f7;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .card ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .card ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        </style>
        <?php
    }
}