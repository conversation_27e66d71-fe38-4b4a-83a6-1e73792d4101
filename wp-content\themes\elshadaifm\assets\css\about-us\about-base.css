/* ==========================================================================
   ABOUT US - BASE STYLES & CONTAINER
   ========================================================================== */

/* Container & Spacing System */
.about-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Headers */
.section-header {
    text-align: center;
    max-width: 700px;
    margin: 0 auto 80px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(139, 92, 246, 0.1);
    color: #8B5CF6;
    padding: 6px 16px;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 20px;
}

.section-title {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3.5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 20px;
    color: #1e293b;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin: 0;
}

/* Animation Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.6s ease;
}

.slide-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.6s ease;
}

.slide-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* Page Loading States */
body:not(.page-loaded) .timeline-item {
    opacity: 0;
    transform: translateY(50px);
}

body.page-loaded .timeline-item {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Smooth scroll enhancement */
html {
    scroll-behavior: smooth;
}

/* Accessibility improvements */
.timeline-item:focus-within {
    outline: 2px solid #8B5CF6;
    outline-offset: 4px;
    border-radius: 8px;
}

.timeline-date:focus {
    outline: 2px solid #ffffff;
    outline-offset: 2px;
}

/* Timeline item focus states for keyboard navigation */
.timeline-item:focus {
    outline: 2px solid #8B5CF6;
    outline-offset: 4px;
    border-radius: 8px;
}

.timeline-item:focus .timeline-content {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 60px rgba(139, 92, 246, 0.2);
}

/* Enhanced timeline date hover effects */
.timeline-date:hover {
    cursor: pointer;
}

.timeline-date.now:hover {
    background: linear-gradient(135deg, #059669, #047857);
}
