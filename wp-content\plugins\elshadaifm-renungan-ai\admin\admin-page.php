<div class="wrap">
    <h1><?php echo esc_html__('Renungan AI Dashboard', 'elshadaifm-renungan-ai'); ?></h1>

    <div class="efrai-dashboard">
        <div class="efrai-cards">
            <div class="efrai-card">
                <h3><?php echo esc_html__('Total Renungan', 'elshadaifm-renungan-ai'); ?></h3>
                <div class="efrai-stat"><?php echo wp_count_posts('renungan')->publish; ?></div>
            </div>
            <div class="efrai-card">
                <h3><?php echo esc_html__('AI Generated', 'elshadaifm-renungan-ai'); ?></h3>
                <div class="efrai-stat"><?php echo $this->count_ai_generated_renungan(); ?></div>
            </div>
            <div class="efrai-card">
                <h3><?php echo esc_html__('With Images', 'elshadaifm-renungan-ai'); ?></h3>
                <div class="efrai-stat"><?php echo $this->count_renungan_with_images(); ?></div>
            </div>
        </div>

        <div class="efrai-section">
            <h2><?php echo esc_html__('Quick Actions', 'elshadaifm-renungan-ai'); ?></h2>
            <div class="efrai-actions">
                <a href="<?php echo admin_url('admin.php?page=efrai-generate'); ?>" class="button button-primary">
                    <?php echo esc_html__('Generate New Renungan', 'elshadaifm-renungan-ai'); ?>
                </a>
                <a href="<?php echo admin_url('post-new.php?post_type=renungan'); ?>" class="button">
                    <?php echo esc_html__('Add Manual Renungan', 'elshadaifm-renungan-ai'); ?>
                </a>
                <a href="<?php echo admin_url('edit.php?post_type=renungan'); ?>" class="button">
                    <?php echo esc_html__('Manage All Renungan', 'elshadaifm-renungan-ai'); ?>
                </a>
            </div>
        </div>

        <div class="efrai-section">
            <h2><?php echo esc_html__('Recent Renungan', 'elshadaifm-renungan-ai'); ?></h2>
            <?php
            $recent_renungan = get_posts(array(
                'post_type' => 'renungan',
                'post_status' => 'publish',
                'numberposts' => 5,
                'orderby' => 'date',
                'order' => 'DESC'
            ));

            if ($recent_renungan) :
            ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php echo esc_html__('Title', 'elshadaifm-renungan-ai'); ?></th>
                        <th><?php echo esc_html__('Date', 'elshadaifm-renungan-ai'); ?></th>
                        <th><?php echo esc_html__('Verse', 'elshadaifm-renungan-ai'); ?></th>
                        <th><?php echo esc_html__('Theme', 'elshadaifm-renungan-ai'); ?></th>
                        <th><?php echo esc_html__('Actions', 'elshadaifm-renungan-ai'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recent_renungan as $post) : ?>
                    <tr>
                        <td>
                            <a href="<?php echo get_edit_post_link($post->ID); ?>">
                                <?php echo esc_html($post->post_title); ?>
                            </a>
                        </td>
                        <td><?php echo esc_html(get_post_meta($post->ID, '_efrai_tanggal', true)); ?></td>
                        <td><?php echo esc_html(get_post_meta($post->ID, '_efrai_ayat', true)); ?></td>
                        <td><?php echo esc_html(get_post_meta($post->ID, '_efrai_tema', true)); ?></td>
                        <td>
                            <a href="<?php echo get_edit_post_link($post->ID); ?>" class="button button-small">
                                <?php echo esc_html__('Edit', 'elshadaifm-renungan-ai'); ?>
                            </a>
                            <a href="<?php echo get_permalink($post->ID); ?>" class="button button-small" target="_blank">
                                <?php echo esc_html__('View', 'elshadaifm-renungan-ai'); ?>
                            </a>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else : ?>
            <p><?php echo esc_html__('No renungan found. Create your first renungan!', 'elshadaifm-renungan-ai'); ?></p>
            <?php endif; ?>
        </div>
    </div>
</div>