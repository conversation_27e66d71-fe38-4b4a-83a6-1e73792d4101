<?php
/**
 * Date Utilities for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class Efrai_Date_Utils {

    /**
     * Extract single date from content (legacy method)
     */
    public function extract_date_from_content($content) {
        $patterns = array(
            '/([A-Z]+,\s+\d{1,2}\s+[A-Z]+\s+\d{4})/i', // SENIN, 22 SEPTEMBER 2025
            '/(\d{1,2}\s+[A-Z]+\s+\d{4})/i', // 22 September 2025
            '/(\d{1,2}-\d{1,2}\s+[A-Z]+\s+\d{4})/i', // 22-28 September 2025
            '/(\d{4}-\d{2}-\d{2})/i' // 2025-09-22
        );

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                return $this->normalize_date($matches[1]);
            }
        }

        return false;
    }

    /**
     * Extract multiple dates from content
     */
    public function extract_multiple_dates_from_content($content) {
        if (empty($content)) {
            return array();
        }

        $patterns = array(
            '/([A-Z]+,\s+\d{1,2}\s+[A-Z]+\s+\d{4})/i', // SENIN, 22 SEPTEMBER 2025
            '/(\d{1,2}\s+[A-Z]+\s+\d{4})/i', // 22 September 2025
            '/(\d{1,2}-\d{1,2}\s+[A-Z]+\s+\d{4})/i', // 22-28 September 2025
            '/(\d{1,2}\/\d{1,2}\/\d{4})/i', // 22/09/2025
            '/(\d{4}-\d{2}-\d{2})/i' // 2025-09-22
        );

        $dates = array();
        $seen_dates = array();

        try {
            foreach ($patterns as $pattern) {
                if (preg_match_all($pattern, $content, $matches)) {
                    foreach ($matches[1] as $match) {
                        $date = $this->normalize_date($match);
                        if ($date && !in_array($date, $seen_dates)) {
                            $seen_dates[] = $date;
                            $formatted = $this->format_date_for_display($date);
                            $dates[] = array(
                                'date' => $date,
                                'formatted' => $formatted
                            );
                        }
                    }
                }
            }
        } catch (Exception $e) {
            error_log('EfraI: Error extracting dates: ' . $e->getMessage());
            return array();
        }

        return $dates;
    }

    /**
     * Normalize date to YYYY-MM-DD format
     */
    public function normalize_date($date_string) {
        try {
            if (empty($date_string)) {
                return false;
            }

            // Handle various formats
            if (strpos($date_string, ',') !== false) {
                // Format: SENIN, 22 SEPTEMBER 2025
                $date_string = preg_replace('/^[A-Z]+,\s*/', '', $date_string);
            }

            if (strpos($date_string, '-') !== false && strpos($date_string, '/') === false) {
                // Format: 22-28 September 2025 - use first date
                $parts = explode('-', $date_string);
                $date_string = trim($parts[0]);
            }

            // Handle DD/MM/YYYY format
            if (strpos($date_string, '/') !== false) {
                $parts = explode('/', $date_string);
                if (count($parts) === 3) {
                    $date_string = $parts[2] . '-' . $parts[1] . '-' . $parts[0];
                }
            }

            // Try to parse with DateTime
            $date = new DateTime($date_string);
            return $date->format('Y-m-d');
        } catch (Exception $e) {
            error_log('EfraI: Date normalization error for "' . $date_string . '": ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Format date for display
     */
    public function format_date_for_display($date_string) {
        try {
            $date = new DateTime($date_string);
            $months = array(
                'January' => 'Januari', 'February' => 'Februari', 'March' => 'Maret',
                'April' => 'April', 'May' => 'Mei', 'June' => 'Juni',
                'July' => 'Juli', 'August' => 'Agustus', 'September' => 'September',
                'October' => 'Oktober', 'November' => 'November', 'December' => 'Desember'
            );

            $english_month = $date->format('F');
            $indonesian_month = isset($months[$english_month]) ? $months[$english_month] : $english_month;

            return $date->format('d') . ' ' . $indonesian_month . ' ' . $date->format('Y');
        } catch (Exception $e) {
            return $date_string;
        }
    }

    /**
     * Generate content preview for specific date
     */
    public function generate_content_preview_for_date($content, $date) {
        // Extract content around the date
        $date_patterns = array(
            '/([A-Z]+,\s+\d{1,2}\s+[A-Z]+\s+\d{4}.*?(?=[A-Z]+,\s+\d{1,2}\s+[A-Z]+\s+\d{4}|$))/is',
            '/(\d{1,2}\s+[A-Z]+\s+\d{4}.*?(?=\d{1,2}\s+[A-Z]+\s+\d{4}|$))/is'
        );

        foreach ($date_patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                $preview = trim($matches[1]);
                // Limit preview length
                if (strlen($preview) > 200) {
                    $preview = substr($preview, 0, 200) . '...';
                }
                return $preview;
            }
        }

        // Fallback: first 200 characters
        return substr($content, 0, 200) . '...';
    }

    /**
     * Generate title for specific date
     */
    public function generate_title_for_date($date) {
        try {
            $date_obj = new DateTime($date);
            $day_names = array(
                'Sunday' => 'Minggu', 'Monday' => 'Senin', 'Tuesday' => 'Selasa',
                'Wednesday' => 'Rabu', 'Thursday' => 'Kamis', 'Friday' => 'Jumat', 'Saturday' => 'Sabtu'
            );

            $english_day = $date_obj->format('l');
            $indonesian_day = isset($day_names[$english_day]) ? $day_names[$english_day] : $english_day;

            return 'Renungan Harian ' . $indonesian_day . ', ' . $this->format_date_for_display($date);
        } catch (Exception $e) {
            return 'Renungan Harian';
        }
    }

    /**
     * Get renungan by date
     */
    public function get_renungan_by_date($date) {
        $args = array(
            'post_type' => 'renungan',
            'post_status' => array('publish', 'draft', 'pending'),
            'meta_query' => array(
                array(
                    'key' => '_efrai_tanggal',
                    'value' => $date,
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1
        );

        return get_posts($args);
    }
}