<?php
/**
 * Post Types and Template Loading for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class EfraI_Post_Types {

    /**
     * Register custom post types
     */
    public function register_post_types() {
        // Register Renungan post type
        register_post_type('renungan', array(
            'labels' => array(
                'name' => __('Renungan', 'elshadaifm-renungan-ai'),
                'singular_name' => __('Renungan', 'elshadaifm-renungan-ai'),
                'menu_name' => __('Renungan', 'elshadaifm-renungan-ai'),
                'add_new' => __('Tambah Baru', 'elshadaifm-renungan-ai'),
                'add_new_item' => __('Tambah Renungan Baru', 'elshadaifm-renungan-ai'),
                'edit_item' => __('Edit Renungan', 'elshadaifm-renungan-ai'),
                'new_item' => __('Renungan Baru', 'elshadaifm-renungan-ai'),
                'view_item' => __('Lihat Renungan', 'elshadaifm-renungan-ai'),
                'search_items' => __('Cari Renungan', 'elshadaifm-renungan-ai'),
                'not_found' => __('Tidak ada renungan ditemukan', 'elshadaifm-renungan-ai'),
                'not_found_in_trash' => __('Tidak ada renungan di sampah', 'elshadaifm-renungan-ai'),
                'all_items' => __('Semua Renungan', 'elshadaifm-renungan-ai'),
                'archives' => __('Arsip Renungan', 'elshadaifm-renungan-ai'),
                'insert_into_item' => ('Masukkan ke renungan'),
                'uploaded_to_this_item' => ('Diunggah ke renungan ini'),
                'filter_items_list' => ('Filter daftar renungan'),
                'items_list_navigation' => ('Navigasi daftar renungan'),
                'items_list' => ('Daftar renungan'),
            ),
            'public' => true,
            'has_archive' => true,
            'rewrite' => array('slug' => 'renungan'),
            'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
            'menu_icon' => 'dashicons-book-alt',
            'show_in_rest' => true,
            'taxonomies' => array('category', 'post_tag'),
        ));
    }

    /**
     * Template loader for custom post types
     */
    public function template_loader($template) {
        if (is_post_type_archive('renungan') || is_singular('renungan')) {
            $theme_template = locate_template(array('renungan.php', 'archive-renungan.php', 'single-renungan.php'));
            if ($theme_template) {
                return $theme_template;
            }
        }
        return $template;
    }
}