<?php
/*
Plugin Name: Elshadaifm Renungan API
Description: API untuk menerima dan menyimpan renungan harian dengan hashing untuk mencegah duplikat
Version: 1.0
Author: Claude Code
*/

if (!defined('ABSPATH')) {
    exit;
}

// Include required files
require_once plugin_dir_path(__FILE__) . 'modules/index.php';
require_once plugin_dir_path(__FILE__) . 'includes/admin-interface.php';
require_once plugin_dir_path(__FILE__) . 'includes/api-endpoints.php';
require_once plugin_dir_path(__FILE__) . 'includes/database-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/openai-service.php';

class Elshadaifm_Renungan_API {

    private $admin;
    private $api_endpoints;
    private $db_manager;
    private $openai_service;
    private $config;
    private $validator;
    private $auth;
    private $logger;
    private $helper;

    public function __construct() {
        $this->initialize_components();
        $this->register_hooks();
    }

    private function initialize_components() {
        // Initialize all modules
        Elshadaifm_Renungan_Modules::initialize();
        $modules = Elshadaifm_Renungan_Modules::get_modules();

        // Assign modules to instance variables
        $this->config = $modules['config'];
        $this->validator = $modules['validator'];
        $this->auth = $modules['auth'];
        $this->logger = $modules['logger'];
        $this->helper = $modules['helper'];

        // Initialize core components
        $this->db_manager = new Elshadaifm_Renungan_DB_Manager();
        $this->openai_service = new Elshadaifm_Renungan_OpenAI_Service();
        $this->admin = new Elshadaifm_Renungan_Admin();
        $this->api_endpoints = new Elshadaifm_Renungan_API_Endpoints($this->db_manager, $this->openai_service);
    }

    private function register_hooks() {
        // Initialize components
        add_action('rest_api_init', array($this->api_endpoints, 'register_routes'));
        add_action('admin_menu', array($this->admin, 'add_admin_menu'));
        add_action('admin_init', array($this->admin, 'register_settings'));

        // Create database tables on plugin activation
        register_activation_hook(__FILE__, array($this, 'activate'));
    }

    public function activate() {
        Elshadaifm_Renungan_Logger::log('Plugin activation started', Elshadaifm_Renungan_Logger::INFO);

        try {
            $this->db_manager->create_tables();
            Elshadaifm_Renungan_Config::initialize_options();

            Elshadaifm_Renungan_Logger::log('Plugin activation completed successfully', Elshadaifm_Renungan_Logger::INFO);
        } catch (Exception $e) {
            Elshadaifm_Renungan_Logger::log_error('Plugin activation failed: ' . $e->getMessage());
            throw $e;
        }
    }
}

// Initialize the plugin
new Elshadaifm_Renungan_API();