# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a WordPress installation for "Elshadaifm", a Christian radio station website running on PHP with MySQL database backend. The project is hosted under XAMPP on Windows (`D:\xampp82\htdocs\elshadaifm`). The site features a modern purple gradient design with radio station functionality including broadcast schedules, team profiles, daily devotions, and testimonial management.

**Database Configuration:**
- Database: `elshadaifm2`
- User: `root` (no password)
- Host: `localhost`
- Table prefix: `efm_`

## Development Environment Setup

**Prerequisites:**
- XAMPP (or similar LAMP/WAMP stack) with PHP 7.2+ and MySQL 5.5+
- Web browser for testing
- WordPress admin access

**Running the Site:**
1. Ensure XAMPP Apache and MySQL services are running
2. Access via: `http://localhost/elshadaifm/`
3. WordPress admin: `http://localhost/elshadaifm/wp-admin/`

## Development Commands

**Database Management:**
```bash
# Access database via phpMyAdmin
http://localhost/phpmyadmin/

# Debug URL parameters
http://localhost/elshadaifm/?debug_instagram=1     # Instagram debugging
http://localhost/elshadaifm/?clear_instagram_cache=1  # Clear Instagram cache
```

**Theme Development:**
```bash
# Theme location
wp-content/themes/elshadaifm/

# Key files to modify:
- functions.php     # Theme setup, custom post types, meta boxes
- style.css         # Main styling with purple gradient theme
- index.php         # Main template
- front-page.php    # Homepage layout
- single-*.php      # Single post templates for custom post types
```

**Plugin Development:**
```bash
# Custom plugins location
wp-content/plugins/elshadaifm-*/
```

## Architecture

### Core WordPress Structure
- `wp-admin/` - WordPress administration files
- `wp-includes/` - Core WordPress functionality
- `wp-content/` - User content (themes, plugins, uploads)
- `wp-config.php` - Main configuration file with database credentials
- `index.php` - Application entry point

### Custom Theme: Elshadaifm
**Location:** `wp-content/themes/elshadaifm/`

**Key Features:**
- Modern purple gradient design system
- Custom post types: Staff, Kesaksian (Testimonials), Renungan (Devotions)
- Instagram integration with auto-fetching
- Radio player functionality
- Responsive design with mobile-first approach

**Custom Post Types:**
- **Staff**: Team members with positions, bios, social links
- **Kesaksian**: User testimonials with moderation system
- **Renungan**: Daily devotions with scripture references

**Key Template Files:**
- `functions.php` - Theme setup, custom post types, Instagram integration
- `style.css` - Main styling (1600+ lines with purple gradient theme)
- `single-kesaksian.php` - Testimonial single post template
- `single-renungan.php` - Devotion single post template
- `page/front-page.php` - Custom homepage layout

### Custom Plugins

**elshadaifm-analytics:**
- Analytics tracking plugin
- Admin interface for statistics
- Location: `wp-content/plugins/elshadaifm-analytics/`

**elshadaifm-broadcast-schedule:**
- Radio broadcast schedule management
- Broadcaster profiles with photos and bios
- Custom post types for broadcasters and schedules
- Shortcodes: `[broadcast_schedule]`, `[broadcaster_profile]`
- Live status indicators
- Database table: `wp_efbs_schedules`

### Key Features

**Instagram Integration:**
- Auto-fetches posts from @elshaddai_radio
- Cached for 30 minutes
- Multiple fallback methods for data extraction
- Debug interface at `?debug_instagram=1`

**Testimonial System:**
- Public submission form
- Admin moderation workflow
- Email notifications for new submissions
- Rate limiting and spam protection
- Custom admin columns for management

**Broadcast Schedule:**
- Regular weekly schedules
- Special event scheduling
- Live status detection
- Responsive frontend display
- Broadcaster profile management

## Development Workflow

### Theme Development
**Adding New Custom Post Types:**
1. Register in `functions.php` using `register_post_type()`
2. Add meta boxes with `add_meta_box()`
3. Create single-{post_type}.php template
4. Add styles in `style.css`

**Modifying Homepage Sections:**
1. Edit `page/front-page.php`
2. Add corresponding styles in `style.css`
3. Register any new widget areas in `functions.php`

### Plugin Development
**Adding New Shortcodes:**
1. Create shortcode function in main plugin file
2. Register with `add_shortcode()`
3. Add corresponding CSS in assets/css/
4. Include template files in templates/ directory

**Database Changes:**
- Use WordPress functions for database operations
- Remember table prefix is `efm_` not default `wp_`
- Use `$wpdb->prefix` in queries

### Debugging
**Enable Debug Mode:**
```php
// In wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

**Instagram Debugging:**
- Visit `?debug_instagram=1` for detailed Instagram fetch logs
- Check `wp-content/debug.log` for error details
- Clear cache with `?clear_instagram_cache=1`

## Key Development Guidelines

### Code Standards
- **Never modify core WordPress files** - use themes, plugins, or child themes
- **Follow WordPress Coding Standards** for PHP, CSS, JavaScript, and HTML
- **Use WordPress functions** instead of direct database queries when possible
- **Theme customizations** should go in the active theme directory
- **Plugin functionality** should be in separate plugin files

### Security
- **Never commit database credentials or WordPress security keys**
- Use nonce verification for forms and AJAX requests
- Sanitize all user inputs
- Validate user capabilities
- Use prepared statements for database queries

### Performance
- Use WordPress transients for caching
- Optimize images before upload
- Minimize external HTTP requests
- Use WordPress enqueue system for scripts/styles

## Testing

**Manual Testing:**
- Browse website functionality
- Test WordPress admin interface
- Verify theme appearance and functionality
- Test custom post type creation and display
- Verify Instagram integration

**Database Testing:**
- Use phpMyAdmin at `http://localhost/phpmyadmin/`
- Database name: `elshadaifm2`
- Check custom tables: `wp_efbs_schedules`

**Feature Testing:**
- Instagram gallery display
- Testimonial submission workflow
- Broadcast schedule live status
- Responsive design on mobile devices
- Radio player functionality

## File Structure Patterns

**Theme Templates:**
- `page-{slug}.php` - Custom page templates
- `single-{post_type}.php` - Single post type templates
- `template-parts/` - Reusable template components
- `inc/` - Include files for specific functionality

**Plugin Structure:**
- `plugin-name.php` - Main plugin file
- `admin/` - Admin interface files
- `assets/` - CSS, JS, images
- `templates/` - Frontend templates
- `includes/` - Helper functions

**CSS Organization:**
- Main styles in `style.css`
- Navigation styles in `css/navigation.css`
- Component-based styling with utility classes
- Purple gradient theme with consistent spacing