<?php
/**
 * OpenAI Integration for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class EfraI_OpenAI {

    private $api_key;
    private $model;

    public function __construct($api_key, $model = 'gpt-4o-mini') {
        $this->api_key = $api_key;
        $this->model = $model;
    }

    /**
     * Test OpenAI connection
     */
    public function test_connection() {
        if (empty($this->api_key)) {
            throw new Exception('OpenAI API key not configured');
        }

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array(
                'model' => $this->model,
                'messages' => array(
                    array(
                        'role' => 'user',
                        'content' => 'Hello, this is a test message.'
                    )
                ),
                'max_tokens' => 10
            )),
            'timeout' => 15
        ));

        if (is_wp_error($response)) {
            throw new Exception('Connection failed: ' . $response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $error_msg = isset($body['error']['message']) ? $body['error']['message'] : 'Unknown error';
            throw new Exception("API Error ($response_code): $error_msg");
        }

        return "✅ OpenAI connection successful! Model: {$this->model}";
    }

    /**
     * Extract dates and generate content for all dates from input content
     */
    public function extract_dates_and_generate_content($input_content) {
        if (empty($this->api_key)) {
            throw new Exception('OpenAI API key not configured');
        }

        $prompt = "Berdasarkan konten renungan berikut, extract tanggal dan generate renungan lengkap:

$input_content

Untuk setiap tanggal yang ditemukan, extract semua ayat dengan bagian (SIANG/MALAM):
- Judul yang menarik
- Verses dalam format array: [{ayat: \"Ayat Alkitab\", isi: \"Isi renungan\"}, ...]
- Tema pembahasan
- Isi renungan yang lengkap dan inspiratif

Output JSON format:
{
  \"dates\": [
    {
      \"date\": \"YYYY-MM-DD\",
      \"formatted_date\": \"DD Month YYYY\",
      \"title\": \"Judul Renungan\",
      \"verses\": [
        {
          \"ayat\": \"Ayat Alkitab\",
          \"isi\": \"Isi renungan untuk bagian ini\"
        }
      ],
      \"theme\": \"Tema Pembahasan\",
      \"content\": \"<p>Isi renungan lengkap dalam format HTML</p>\"
    }
  ]
}

Contoh input:
SENIN, 22 SEPTEMBER 2025
Sadari dimensi ROH itu NYATA, maka engkau akan MENANG.
1 Yohanes 5:4 Sebab semua yang lahir dari Allah, mengalahkan dunia.
SIANG
Iman yang BERKOBAR-KOBAR akan membawamu dari KEMENANGAN kepada kemenangan.
1 Korintus 15:57 Tetapi syukur kepada Allah, yang telah memberikan kepada kita kemenangan.
MALAM
Masalah yang tampak sering BERAKAR dari PEPERANGAN ROHANI yang tak tampak.
Efesus 6:12 Karena perjuangan kita bukanlah melawan darah dan daging.

Rules:
- Extract tanggal dan semua ayat dengan bagiannya (PAGI/SIANG/MALAM)
- Simpan verses sebagai array of objects dengan key ayat dan isi
- Generate konten renungan yang relevan
- Output valid JSON only";

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array(
                'model' => $this->model,
                'messages' => array(
                    array(
                        'role' => 'system',
                        'content' => 'Kamu adalah asisten AI yang ahli dalam membuat renungan harian Kristen. Extract tanggal dari konten dan generate renungan lengkap untuk setiap tanggal.'
                    ),
                    array(
                        'role' => 'user',
                        'content' => $prompt
                    )
                ),
                'max_tokens' => 1000,
                'temperature' => 0.7
            )),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            throw new Exception('Failed to extract dates and generate content: ' . $response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $error_msg = isset($body['error']['message']) ? $body['error']['message'] : 'Unknown error';
            throw new Exception("API Error ($response_code): $error_msg");
        }

        $content = $body['choices'][0]['message']['content'];

        // Parse JSON response
        return $this->parse_json_response($content);
    }

    /**
     * Parse JSON response from OpenAI
     */
    private function parse_json_response($content) {
        // Debug log
        error_log('EfraI: Raw AI Response: ' . $content);

        // Try to extract JSON from response
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $json_str = $matches[0];
            error_log('EfraI: Extracted JSON: ' . $json_str);
            $data = json_decode($json_str, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                error_log('EfraI: JSON decode success: ' . print_r($data, true));
                if (isset($data['dates'])) {
                    return $data['dates'];
                } else {
                    error_log('EfraI: No dates key in JSON');
                }
            } else {
                error_log('EfraI: JSON decode error: ' . json_last_error_msg());
            }
        }

        // Fallback: try to parse the entire content as JSON
        $data = json_decode($content, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            error_log('EfraI: Full content JSON decode success: ' . print_r($data, true));
            if (isset($data['dates'])) {
                return $data['dates'];
            } else {
                error_log('EfraI: No dates key in full content JSON');
            }
        } else {
            error_log('EfraI: Full content JSON decode error: ' . json_last_error_msg());
        }

        // Try to extract dates manually as fallback
        error_log('EfraI: Attempting manual extraction');
        return $this->manual_extraction_fallback($content);
    }

    /**
     * Manual extraction fallback if JSON parsing fails
     */
    private function manual_extraction_fallback($content) {
        $dates = array();

        // Extract date patterns
        if (preg_match_all('/(\d{1,2}\s+[A-Z][a-z]+\s+\d{4})/', $content, $date_matches)) {
            foreach ($date_matches[1] as $index => $date_str) {
                // Convert to Y-m-d format
                try {
                    $date = new DateTime($date_str);
                    $ymd_date = $date->format('Y-m-d');

                    // Extract title (look for patterns like "Judul:" or title-like content)
                    $title = "Renungan untuk " . $date_str;
                    if (preg_match('/(?:Judul|Title):\s*(.+)/i', $content, $title_match)) {
                        $title = trim($title_match[1]);
                    }

                    $dates[] = array(
                        'date' => $ymd_date,
                        'formatted_date' => $date_str,
                        'title' => $title,
                        'verse' => '',
                        'theme' => '',
                        'content' => substr($content, 0, 500) . '...'
                    );
                } catch (Exception $e) {
                    error_log('EfraI: Date parse error for ' . $date_str);
                }
            }
        }

        error_log('EfraI: Manual extraction result: ' . print_r($dates, true));
        return $dates;
    }

    /**
     * Generate renungan content from OpenAI (legacy method for single date)
     */
    public function generate_renungan_content($tanggal, $input_content, $custom_title = null) {
        if (empty($this->api_key)) {
            throw new Exception('OpenAI API key not configured');
        }

        $title_instruction = $custom_title ? "Gunakan judul: \"$custom_title\"" : "Buatkan judul yang menarik";

        $prompt = "Berdasarkan konten renungan berikut, buatkan renungan lengkap untuk tanggal $tanggal:

$input_content

Format output:
Judul: [$title_instruction]
Tanggal: $tanggal
Ayat: [ayat utama]
Tema: [tema pembahasan]
Konten: [isi renungan lengkap dalam format HTML]";

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $this->api_key,
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode(array(
                'model' => $this->model,
                'messages' => array(
                    array(
                        'role' => 'system',
                        'content' => 'Kamu adalah asisten AI yang ahli dalam membuat renungan harian Kristen. Buatkan renungan yang inspiratif, mendalam, dan relevan dengan kehidupan sehari-hari.'
                    ),
                    array(
                        'role' => 'user',
                        'content' => $prompt
                    )
                ),
                'max_tokens' => 2000,
                'temperature' => 0.7
            )),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            throw new Exception('Failed to generate content: ' . $response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $error_msg = isset($body['error']['message']) ? $body['error']['message'] : 'Unknown error';
            throw new Exception("API Error ($response_code): $error_msg");
        }

        $content = $body['choices'][0]['message']['content'];

        // Parse the response
        return $this->parse_openai_response($content);
    }

    /**
     * Parse OpenAI response to extract structured data
     */
    private function parse_openai_response($content) {
        $result = array(
            'judul' => '',
            'tanggal' => '',
            'ayat' => '',
            'tema' => '',
            'konten' => ''
        );

        // Extract Judul
        if (preg_match('/Judul:\s*(.+?)(?=\nTanggal:|\nAyat:|\nTema:|\nKonten:|$)/is', $content, $matches)) {
            $result['judul'] = trim($matches[1]);
        }

        // Extract Tanggal
        if (preg_match('/Tanggal:\s*(.+?)(?=\nAyat:|\nTema:|\nKonten:|$)/is', $content, $matches)) {
            $result['tanggal'] = trim($matches[1]);
        }

        // Extract Ayat
        if (preg_match('/Ayat:\s*(.+?)(?=\nTema:|\nKonten:|$)/is', $content, $matches)) {
            $result['ayat'] = trim($matches[1]);
        }

        // Extract Tema
        if (preg_match('/Tema:\s*(.+?)(?=\nKonten:|$)/is', $content, $matches)) {
            $result['tema'] = trim($matches[1]);
        }

        // Extract Konten
        if (preg_match('/Konten:\s*(.+?)(?=$)/is', $content, $matches)) {
            $result['konten'] = trim($matches[1]);
        }

        // If parsing failed, try to extract title from first line
        if (empty($result['judul'])) {
            $lines = explode("\n", $content);
            foreach ($lines as $line) {
                if (!empty(trim($line)) && !preg_match('/^(Judul|Tanggal|Ayat|Tema|Konten):\s*/i', $line)) {
                    $result['judul'] = trim($line);
                    break;
                }
            }
        }

        // If still no title, create a generic one
        if (empty($result['judul'])) {
            $result['judul'] = 'Renungan Harian';
        }

        // If no content, use the whole response
        if (empty($result['konten'])) {
            $result['konten'] = $content;
        }

        return $result;
    }

    /**
     * Update API key and model
     */
    public function update_config($api_key, $model = null) {
        $this->api_key = $api_key;
        if ($model) {
            $this->model = $model;
        }
    }

    /**
     * Get current model
     */
    public function get_model() {
        return $this->model;
    }
}