<?php
/**
 * Shortcodes for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class EfraI_Shortcodes {

    private $main_plugin;

    public function __construct($main_plugin) {
        $this->main_plugin = $main_plugin;
    }

    /**
     * Register all shortcodes
     */
    public function register_shortcodes() {
        add_shortcode('renungan_harian', array($this, 'renungan_shortcode'));
        add_shortcode('renungan_terbaru', array($this, 'latest_renungan_shortcode'));
    }

    /**
     * Shortcode for daily renungan
     */
    public function renungan_shortcode($atts) {
        $atts = shortcode_atts(array(
            'date' => date('Y-m-d'),
        ), $atts, 'renungan_harian');

        $posts = $this->main_plugin->get_date_utils()->get_renungan_by_date($atts['date']);

        if (empty($posts)) {
            return '<p>' . __('Renungan not available for this date.', 'elshadaifm-renungan-ai') . '</p>';
        }

        $post = $posts[0];
        $meta = $this->main_plugin->get_utils()->get_renungan_meta($post->ID);

        ob_start();
        ?>
        <div class="efrai-renungan">
            <h3><?php echo esc_html($post->post_title); ?></h3>
            <?php if ($meta['tanggal']) : ?>
                <p class="efrai-date"><?php echo esc_html($meta['tanggal']); ?></p>
            <?php endif; ?>
            <?php if ($meta['ayat']) : ?>
                <p class="efrai-verse"><strong><?php echo esc_html($meta['ayat']); ?></strong></p>
            <?php endif; ?>
            <div class="efrai-content">
                <?php echo $this->main_plugin->get_utils()->safe_output($post->post_content); ?>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }

    /**
     * Shortcode for latest renungan
     */
    public function latest_renungan_shortcode($atts) {
        $atts = shortcode_atts(array(
            'limit' => 5,
        ), $atts, 'renungan_terbaru');

        $posts = $this->main_plugin->get_utils()->get_latest_renungan($atts['limit']);

        if (empty($posts)) {
            return '<p>' . __('No renungan available.', 'elshadaifm-renungan-ai') . '</p>';
        }

        ob_start();
        ?>
        <div class="efrai-latest-renungan">
            <?php foreach ($posts as $post) : ?>
                <div class="efrai-item">
                    <h4><a href="<?php echo get_permalink($post->ID); ?>"><?php echo esc_html($post->post_title); ?></a></h4>
                    <p class="efrai-date"><?php echo get_the_date('', $post->ID); ?></p>
                </div>
            <?php endforeach; ?>
        </div>
        <?php
        return ob_get_clean();
    }
}