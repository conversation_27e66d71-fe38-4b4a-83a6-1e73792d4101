<div class="efrai-latest-renungan">
    <?php while ($query->have_posts()) : $query->the_post(); ?>
        <div class="efrai-latest-item">
            <?php if ($atts['show_image'] === 'yes' && has_post_thumbnail()) : ?>
                <div class="efrai-latest-image">
                    <a href="<?php the_permalink(); ?>">
                        <?php the_post_thumbnail('thumbnail'); ?>
                    </a>
                </div>
            <?php endif; ?>

            <div class="efrai-latest-content">
                <h4 class="efrai-latest-title">
                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                </h4>

                <?php if ($atts['show_date'] === 'yes') : ?>
                    <?php if ($tanggal = get_post_meta(get_the_ID(), '_efrai_tanggal', true)) : ?>
                        <div class="efrai-latest-date">
                            <span class="dashicons dashicons-calendar-alt"></span>
                            <?php echo esc_html($tanggal); ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

                <?php if ($atts['show_excerpt'] === 'yes') : ?>
                    <div class="efrai-latest-excerpt">
                        <?php echo wp_trim_words(get_the_excerpt(), 15, '...'); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endwhile; ?>
    <?php wp_reset_postdata(); ?>

    <?php if (intval($atts['limit']) > 0) : ?>
        <div class="efrai-latest-footer">
            <a href="<?php echo get_post_type_archive_link('renungan'); ?>" class="efrai-view-all">
                <?php echo esc_html__('Lihat Semua Renungan', 'elshadaifm-renungan-ai'); ?>
                <span class="dashicons dashicons-arrow-right-alt2"></span>
            </a>
        </div>
    <?php endif; ?>
</div>