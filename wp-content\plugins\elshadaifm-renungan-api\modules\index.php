<?php
/**
 * Modules Index for Elshadaifm Renungan API
 * Autoloader for all modules
 */

// Define module directory
if (!defined('EFR_MODULES_DIR')) {
    define('EFR_MODULES_DIR', plugin_dir_path(__FILE__));
}

// Include all module files
$module_files = array(
    'config.php',
    'validator.php',
    'auth.php',
    'logger.php',
    'helper.php'
);

foreach ($module_files as $file) {
    require_once EFR_MODULES_DIR . $file;
}

/**
 * Module loader class
 */
class Elshadaifm_Renungan_Modules {

    /**
     * Get all module instances
     */
    public static function get_modules() {
        return array(
            'config' => new Elshadaifm_Renungan_Config(),
            'validator' => new Elshadaifm_Renungan_Validator(),
            'auth' => new Elshadaifm_Renungan_Auth(),
            'logger' => new Elshadaifm_Renungan_Logger(),
            'helper' => new Elshadaifm_Renungan_Helper()
        );
    }

    /**
     * Get specific module instance
     */
    public static function get_module($name) {
        $modules = self::get_modules();
        return isset($modules[$name]) ? $modules[$name] : null;
    }

    /**
     * Initialize all modules
     */
    public static function initialize() {
        // Initialize configuration first
        Elshadaifm_Renungan_Config::initialize_options();

        // Log module initialization
        if (class_exists('Elshadaifm_Renungan_Logger')) {
            Elshadaifm_Renungan_Logger::log('Modules initialized successfully', Elshadaifm_Renungan_Logger::INFO);
        }
    }
}