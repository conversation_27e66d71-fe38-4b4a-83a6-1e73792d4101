/* ==========================================================================
   ABOUT US - INTRODUCTION SECTION
   ========================================================================== */

/* Professional Introduction Section */
.about-intro {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    position: relative;
    overflow: hidden;
    opacity: 1 !important; /* Always visible */
    transform: translateY(0) !important; /* No parallax animation */
    will-change: auto; /* Remove transform optimization */
}

.about-intro::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(139, 92, 246, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.about-intro::after {
    content: '';
    position: absolute;
    bottom: -50%;
    left: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(124, 58, 237, 0.06) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.about-content-wrapper {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
}

.about-header {
    margin-bottom: 60px;
}

.about-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 8px 20px;
    border-radius: 50px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 24px;
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
    transition: all 0.3s ease;
}

.about-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(139, 92, 246, 0.4);
}

.about-title {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #1e293b 0%, #8B5CF6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.8;
    margin: 0 auto;
    max-width: 700px;
}

/* Professional Highlight Cards */
.about-highlights {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.highlight-card {
    background: white;
    padding: 50px 40px;
    border-radius: 24px;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        0 10px 40px rgba(139, 92, 246, 0.08);
    text-align: center;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.highlight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
    transition: left 0.6s ease;
}

.highlight-card:hover::before {
    left: 100%;
}

.highlight-card:hover {
    transform: translateY(-8px);
    box-shadow:
        0 20px 25px -5px rgba(0, 0, 0, 0.1),
        0 10px 10px -5px rgba(0, 0, 0, 0.04),
        0 25px 60px rgba(139, 92, 246, 0.15);
}

.highlight-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 24px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    color: white;
    transition: all 0.3s ease;
}

.highlight-card:hover .highlight-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
}

.highlight-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: #1e293b;
}

.highlight-card p {
    color: #64748b;
    line-height: 1.7;
    margin: 0;
    font-size: 1.1rem;
}
