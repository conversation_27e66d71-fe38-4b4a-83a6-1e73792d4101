/* ==========================================================================
   ABOUT US - VISION & MISSION SECTION
   ========================================================================== */

/* Vision & Mission Section */
.vision-mission-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    opacity: 1 !important; /* Always visible */
    transform: translateY(0) !important; /* Always in position */
}

.vision-mission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.vision-card, .mission-card {
    border-radius: 24px;
    padding: 50px;
    height: 100%;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.vision-card {
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    color: white;
    box-shadow: 0 20px 60px rgba(139, 92, 246, 0.3);
}

.mission-card {
    background: white;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.vision-card::after, .mission-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.vision-card:hover::after, .mission-card:hover::after {
    opacity: 1;
}

.vision-icon, .mission-icon {
    width: 80px;
    height: 80px;
    border-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.vision-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.mission-icon {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
}

.vision-card:hover .vision-icon, .mission-card:hover .mission-icon {
    transform: scale(1.1) rotate(5deg);
}

.vision-card h3, .mission-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 20px;
}

.vision-card h3 {
    color: white;
}

.mission-card h3 {
    color: #1e293b;
}

.vision-card p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    font-size: 1.1rem;
    margin: 0;
}

.mission-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mission-list li {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(139, 92, 246, 0.05);
    border-radius: 16px;
    transition: all 0.3s ease;
    position: relative;
}

.mission-list li:hover {
    transform: translateX(10px);
    background: rgba(139, 92, 246, 0.1);
    padding-left: 30px;
}

.mission-list li i {
    color: #8B5CF6;
    font-size: 1.25rem;
    margin-top: 2px;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.mission-list li:hover i {
    color: #7C3AED;
}

.mission-list li span {
    flex: 1;
    color: #475569;
    line-height: 1.6;
}

.mission-list li strong {
    color: #1e293b;
    font-weight: 600;
}
