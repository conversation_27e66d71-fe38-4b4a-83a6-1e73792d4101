/* ==========================================================================
   ABOUT US - STATISTICS SECTION
   ========================================================================== */

/* Stats Section */
.stats-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
    position: relative;
    overflow: hidden;
    opacity: 1 !important; /* Always visible */
    transform: translateY(0) !important; /* Always in position */
}

.stats-section::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -10%;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.stats-section::after {
    content: '';
    position: absolute;
    bottom: -50%;
    left: -10%;
    width: 500px;
    height: 500px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

.stat-item {
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-family: 'Poppins', sans-serif;
    font-size: 3.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.stat-item:hover .stat-number {
    color: #fbbf24;
    transform: scale(1.1);
}

.stat-label {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}
