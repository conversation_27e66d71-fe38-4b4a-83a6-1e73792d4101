(function($) {
    'use strict';

    // Initialize on document ready
    $(document).ready(function() {
        initContentInput();
        initGeneratePage();
        initMetaBoxTools();
        initSettingsPage();
        initBulkActions();
    });

    // Content input functionality
    function initContentInput() {
        const contentInput = $('#efrai_content_input');
        const extractBtn = $('#efrai_extract_btn');
        const datesList = $('#efrai_extracted_dates');

        if (contentInput.length === 0) return;

        // Handle extract button click
        extractBtn.on('click', function() {
            const content = contentInput.val().trim();

            if (content.length < 10) {
                showNotification('Please enter more content (at least 10 characters)', 'error');
                return;
            }

            extractDatesFromContent(content);
        });

        function extractDatesFromContent(content) {
            extractBtn.prop('disabled', true).html('<span class="dashicons dashicons-spinner"></span> AI Processing...');
            datesList.html('<div class="efrai-loading"><span class="dashicons dashicons-search"></span> AI extracting dates and generating content...</div>');

            $.ajax({
                url: efrai_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'efrai_extract_dates',
                    nonce: efrai_admin_ajax.nonce,
                    content: content
                },
                success: function(response) {
                    console.log('AI Response:', response);
                    if (response.success) {
                        console.log('Dates data:', response.data);
                        displayExtractedDates(response.data);
                        showNotification(`AI found ${response.data.length} date(s) and generated content`, 'success');
                    } else {
                        showNotification(response.data, 'error');
                        datesList.html('<div class="efrai-error"><span class="dashicons dashicons-warning"></span> AI processing failed</div>');
                    }
                },
                error: function() {
                    showNotification('Failed to process with AI', 'error');
                    datesList.html('<div class="efrai-error"><span class="dashicons dashicons-warning"></span> Network error</div>');
                },
                complete: function() {
                    extractBtn.prop('disabled', false).html('Process with AI');
                }
            });
        }

        function displayExtractedDates(dates) {
            console.log('Displaying dates:', dates);
            if (dates.length === 0) {
                datesList.html('<div class="efrai-no-dates"><span class="dashicons dashicons-calendar"></span> No dates found in content</div>');
                return;
            }

            let html = '';
            dates.forEach(function(dateInfo, index) {
                console.log('Processing dateInfo:', dateInfo);
                const statusClass = dateInfo.exists ? 'existing' : 'available';
                const statusText = dateInfo.exists ? 'Already Exists' : 'Available';
                const headerClass = dateInfo.exists ? 'has-existing' : 'available';

                html += `
                    <div class="efrai-date-item">
                        <div class="efrai-date-header ${headerClass}" data-date-index="${index}">
                            <div class="efrai-date-info">
                                <strong>${dateInfo.formatted_date}</strong>
                                <span class="efrai-date-status ${statusClass}">${statusText}</span>
                            </div>
                            <div class="efrai-date-actions">
                                ${!dateInfo.exists ? `<button class="button button-small button-primary efrai-generate-date" data-index="${index}">Publish</button>` : ''}
                                <button class="button button-small efrai-toggle-details" data-date-index="${index}">
                                    <span class="dashicons dashicons-arrow-down"></span>
                                </button>
                            </div>
                        </div>
                        <div class="efrai-date-details" id="efrai-details-${index}">
                            <div class="efrai-date-preview">
                                <div class="efrai-date-content">
                                    <h4>${dateInfo.title}</h4>
                                    <p><strong>Tema:</strong> ${dateInfo.theme || '-'}</p>
                                    <div class="efrai-verses">
                                        <strong>Ayat-ayat:</strong>
                                        ${dateInfo.verses && dateInfo.verses.length > 0 ?
                                            dateInfo.verses.map(v => `
                                                <div class="efrai-verse-item">
                                                    <div class="efrai-verse-reference">${v.ayat || ''}</div>
                                                    <div class="efrai-verse-text">${v.isi || ''}</div>
                                                </div>
                                            `).join('') :
                                            (dateInfo.verse ? dateInfo.verse.split('\n').map(v => `<div class="efrai-verse-item"><div class="efrai-verse-reference">${v.trim()}</div></div>`).join('') : '<div class="efrai-verse-item">-</div>')
                                        }
                                    </div>
                                    <div class="efrai-content-preview">
                                        <strong>Content Preview:</strong>
                                        <div>${dateInfo.content ? dateInfo.content.substring(0, 500) + '...' : 'No content available'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            datesList.html(html);

            // Attach event listeners
            $('.efrai-toggle-details').on('click', function() {
                const index = $(this).data('date-index');
                const details = $(`#efrai-details-${index}`);
                const arrow = $(this).find('.dashicons');

                details.toggleClass('expanded');
                arrow.toggleClass('dashicons-arrow-down dashicons-arrow-up');
            });

            $('.efrai-generate-date').on('click', function() {
                const index = $(this).data('index');
                const dateInfo = dates[index];
                if (dateInfo) {
                    publishRenunganForDate(dateInfo);
                }
            });
        }

        function publishRenunganForDate(dateInfo) {
            const button = $(`.efrai-generate-date[data-index="${dates.findIndex(d => d.date === dateInfo.date)}"]`);
            button.prop('disabled', true).html('<span class="dashicons dashicons-spinner"></span> Publishing...');

            $.ajax({
                url: efrai_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'efrai_generate_for_date',
                    nonce: efrai_admin_ajax.nonce,
                    date: dateInfo.date,
                    title: dateInfo.title,
                    verses: JSON.stringify(dateInfo.verses || []),
                    verse: dateInfo.verse || '',
                    theme: dateInfo.theme || '',
                    content: dateInfo.content || ''
                },
                success: function(response) {
                    if (response.success) {
                        showNotification(`Successfully published renungan for ${dateInfo.formatted_date}!`, 'success');

                        // Open edit URL in new tab
                        if (response.data.edit_url) {
                            window.open(response.data.edit_url, '_blank');
                        }

                        // Refresh the dates list to update status
                        const content = contentInput.val().trim();
                        extractDatesFromContent(content);
                    } else {
                        showNotification(response.data, 'error');
                        button.prop('disabled', false).html('Publish');
                    }
                },
                error: function() {
                    showNotification('Failed to publish renungan', 'error');
                    button.prop('disabled', false).html('Publish');
                }
            });
        }
    }

    // Generate page functionality
    function initGeneratePage() {
        const generateBtn = $('#efrai_generate_btn');
        const contentInput = $('#efrai_content_input');

        if (generateBtn.length === 0) return;

        // Generate and publish renungan
        generateBtn.on('click', function() {
            const content = contentInput.val().trim();

            if (!content) {
                showNotification('Please enter renungan content first.', 'error');
                return;
            }

            if (content.length < 50) {
                showNotification('Content is too short. Please enter more detailed renungan content.', 'error');
                return;
            }

            generateBtn.prop('disabled', true);
            generateBtn.find('.dashicons-spinner').show();

            $.ajax({
                url: efrai_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'efrai_extract_and_publish',
                    nonce: efrai_admin_ajax.nonce,
                    content: content
                },
                success: function(response) {
                    if (response.success) {
                        const data = response.data;
                        showNotification(`Successfully created and published "${data.post_title}"!`, 'success');

                        // Clear the form
                        contentInput.val('');
                        $('#efrai_extracted_date').val('');
                        $('#efrai_existing_check').html('<span class="dashicons dashicons-clock"></span> Enter content above to check for existing renungan').removeClass('success warning error').addClass('info');

                        // Show success message with links
                        setTimeout(() => {
                            if (confirm('Renungan created successfully!\n\nView: ' + data.post_url + '\nEdit: ' + data.edit_url + '\n\nGo to edit page?')) {
                                window.open(data.edit_url, '_blank');
                            }
                        }, 1000);

                    } else {
                        showNotification(response.data, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Error:', xhr, status, error);
                    const errorMsg = xhr.responseJSON && xhr.responseJSON.data ? xhr.responseJSON.data : 'Failed to generate and publish renungan. Please try again.';
                    showNotification(errorMsg, 'error');
                },
                complete: function() {
                    generateBtn.prop('disabled', false);
                    generateBtn.find('.dashicons-spinner').hide();
                }
            });
        });
    }

    // Meta box tools functionality
    function initMetaBoxTools() {
        const generateContentBtn = $('.efrai-generate-content-btn');
        const generateImageBtn = $('.efrai-generate-image-btn');
        const autofillBtn = $('.efrai-autofill-btn');

        // Generate content
        generateContentBtn.on('click', function() {
            const postId = $(this).data('post-id');
            const tema = $('#efrai_tema').val();
            const tanggal = $('#efrai_tanggal').val();

            if (!tema) {
                showNotification('Please enter a theme first.', 'error');
                return;
            }

            showMetaBoxLoading(true);

            // Simulate content generation
            setTimeout(function() {
                const generatedContent = generateContentFromTheme(tema, tanggal);
                if (typeof tinymce !== 'undefined' && tinymce.get('content')) {
                    tinymce.get('content').setContent(generatedContent);
                } else {
                    $('#content').val(generatedContent);
                }
                showMetaBoxMessage('Content generated successfully!', 'success');
                showMetaBoxLoading(false);
            }, 2000);
        });

        // Generate image
        generateImageBtn.on('click', function() {
            const postId = $(this).data('post-id');
            const title = $('#title').val();
            const tema = $('#efrai_tema').val();

            if (!title || !tema) {
                showNotification('Please enter title and theme first.', 'error');
                return;
            }

            showMetaBoxLoading(true);

            const prompt = `Christian devotional image about "${title}" with theme: ${tema}. Beautiful, inspiring, spiritual, modern digital art style.`;

            $.ajax({
                url: efrai_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'efrai_generate_image',
                    nonce: efrai_admin_ajax.nonce,
                    post_id: postId,
                    prompt: prompt
                },
                success: function(response) {
                    if (response.success) {
                        // Refresh the page to show the new image
                        window.location.reload();
                    } else {
                        showMetaBoxMessage(response.data, 'error');
                    }
                },
                error: function() {
                    showMetaBoxMessage('Failed to generate image.', 'error');
                },
                complete: function() {
                    showMetaBoxLoading(false);
                }
            });
        });

        // Auto fill
        autofillBtn.on('click', function() {
            const content = tinymce.get('content') ? tinymce.get('content').getContent() : $('#content').val();
            const parsed = parseContentFromText(content);

            if (parsed.ayat) {
                $('#efrai_ayat').val(parsed.ayat);
            }
            if (parsed.tema) {
                $('#efrai_tema').val(parsed.tema);
            }

            showMetaBoxMessage('Fields auto-filled successfully!', 'success');
        });
    }

    // Settings page functionality
    function initSettingsPage() {
        const testOpenAIBtn = $('#efrai_test_openai_connection');
        const testGoogleAIBtn = $('#efrai_test_google_connection');

        if (testOpenAIBtn.length === 0 && testGoogleAIBtn.length === 0) return;

        // Test OpenAI Connection
        testOpenAIBtn.on('click', function() {
            testOpenAIBtn.prop('disabled', true);
            testConnection('efrai_test_openai_connection', testOpenAIBtn);
        });

        // Test Google AI Connection
        testGoogleAIBtn.on('click', function() {
            testGoogleAIBtn.prop('disabled', true);
            testConnection('efrai_test_google_connection', testGoogleAIBtn);
        });
    }

    function testConnection(action, button) {
        const resultDiv = $('#efrai_test_result');
        resultDiv.hide().removeClass('success error');

        console.log('Testing connection:', action);
        console.log('AJAX URL:', efrai_admin_ajax.ajax_url);
        console.log('Nonce available:', !!efrai_admin_ajax.nonce);

        $.ajax({
            url: efrai_admin_ajax.ajax_url,
            type: 'POST',
            data: {
                action: action,
                nonce: efrai_admin_ajax.nonce
            },
            success: function(response) {
                console.log('Connection test response:', response);
                if (response.success) {
                    resultDiv.addClass('success').text(response.data).show();
                } else {
                    resultDiv.addClass('error').text(response.data).show();
                }
            },
            error: function(xhr, status, error) {
                console.log('Connection test error:', {xhr, status, error});
                resultDiv.addClass('error').text('Failed to test connection. Please check your API key and try again. Error: ' + error).show();
            },
            complete: function() {
                button.prop('disabled', false);
            }
        });
    }

    // Bulk actions functionality
    function initBulkActions() {
        // Handle bulk action notices
        const urlParams = new URLSearchParams(window.location.search);
        const bulkGenerated = urlParams.get('bulk_efrai_generated');

        if (bulkGenerated) {
            showNotification(`${bulkGenerated} images generated successfully!`, 'success');
        }
    }

    // Utility functions
    function displayResult(result) {
        $('#efrai_result_title').text(result.judul);
        $('#efrai_result_date').text(result.tanggal);

        // Handle verses array or fallback to single verse
        let verseText = '';
        if (result.verses && Array.isArray(result.verses) && result.verses.length > 0) {
            verseText = result.verses.map(v => `${v.ayat}: ${v.isi}`).join('\n');
        } else if (result.ayat) {
            verseText = result.ayat;
        }
        $('#efrai_result_verse').text(verseText);

        $('#efrai_result_theme').text(result.tema);
        $('#efrai_result_content').html(result.konten);
    }

    function parseFileContent(content) {
        // Extract dates from content
        const datePattern = /\b([A-Z]+,\s+\d+\s+[A-Z]+\s+\d{4})\b/g;
        const dates = content.match(datePattern) || [];

        // Set the first available date as default
        if (dates.length > 0) {
            const dateStr = dates[0];
            const dateObj = new Date(dateStr.replace(',', ''));
            const formattedDate = dateObj.toISOString().split('T')[0];
            $('#efrai_generate_date').val(formattedDate);
        }
    }

    function parseContentFromText(content) {
        const parsed = {
            ayat: '',
            tema: ''
        };

        // Try to extract verse pattern
        const versePattern = /\b([A-Za-z\s]+\s+\d+:\d+)\b/;
        const verseMatch = content.match(versePattern);
        if (verseMatch) {
            parsed.ayat = verseMatch[1];
        }

        // Try to extract theme from first few sentences
        const sentences = content.split(/[.!?]+/);
        if (sentences.length > 0) {
            parsed.tema = sentences[0].trim();
        }

        return parsed;
    }

    function generateContentFromTheme(tema, tanggal) {
        // Simulate AI content generation
        return `<h2>Renungan Harian</h2>
<p><strong>Tanggal:</strong> ${tanggal || 'Hari ini'}</p>
<p><strong>Tema:</strong> ${tema}</p>

<p>Renungan ini membahas tentang <em>${tema}</em>. Sebagai orang percaya, kita diajak untuk memahami lebih dalam bagaimana tema ini dapat diterapkan dalam kehidupan sehari-hari.</p>

<p>Firman Tuhan mengajarkan kita bahwa setiap aspek kehidupan rohani kita memiliki makna dan tujuan yang dalam. Ketika kita memahami tema ini, kita akan menemukan kekuatan dan pengharapan baru dalam berjalan bersama Tuhan.</p>

<p>Marilah kita merenungkan bagaimana kita dapat mengaplikasikan kebenaran ini dalam kehidupan kita, sehingga kita dapat menjadi saksi yang kuat bagi kemuliaan nama Tuhan.</p>

<p><em>Amin.</em></p>`;
    }

    function showMetaBoxLoading(show) {
        const loading = $('#efrai-tools-loading');
        const message = $('#efrai-tools-message');

        if (show) {
            loading.show();
            message.hide();
        } else {
            loading.hide();
        }
    }

    function showMetaBoxMessage(text, type) {
        const message = $('#efrai-tools-message');
        message.removeClass('success error').addClass(type).text(text).show();

        setTimeout(function() {
            message.fadeOut();
        }, 3000);
    }

    function showNotification(message, type) {
        const notice = $(`<div class="efrai-notice ${type}">${message}</div>`);
        $('body').append(notice);

        setTimeout(function() {
            notice.fadeOut(function() {
                notice.remove();
            });
        }, 3000);
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

})(jQuery);