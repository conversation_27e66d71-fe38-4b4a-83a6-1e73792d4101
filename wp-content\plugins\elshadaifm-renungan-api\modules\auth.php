<?php
/**
 * Authentication Module for Elshadaifm Renungan API
 * Handles API key authentication and authorization
 */

class Elshadaifm_Renungan_Auth {

    /**
     * Authenticate API request
     */
    public static function authenticate($request) {
        $api_key = self::extract_api_key($request);
        $stored_key = self::get_stored_api_key();

        self::log_auth_debug($api_key, $stored_key);

        if (!self::validate_api_key($api_key, $stored_key)) {
            return new WP_Error(
                'authentication_failed',
                'Invalid API key',
                array('status' => 401)
            );
        }

        return true;
    }

    /**
     * Extract API key from request
     */
    private static function extract_api_key($request) {
        // Check Authorization header first
        $headers = $request->get_headers();
        $auth_key = self::extract_from_header($headers);

        if ($auth_key) {
            return $auth_key;
        }

        // Check request parameters
        return self::extract_from_params($request);
    }

    /**
     * Extract API key from Authorization header
     */
    private static function extract_from_header($headers) {
        if (!isset($headers['authorization'])) {
            return false;
        }

        $auth_header = $headers['authorization'][0];

        if (strpos($auth_header, 'Bearer ') === 0) {
            return substr($auth_header, 7);
        }

        return false;
    }

    /**
     * Extract API key from request parameters
     */
    private static function extract_from_params($request) {
        $params = $request->get_params();
        return isset($params['api_key']) ? $params['api_key'] : false;
    }

    /**
     * Get stored API key from WordPress options
     */
    private static function get_stored_api_key() {
        return get_option('elshadaifm_api_key');
    }

    /**
     * Validate API key
     */
    private static function validate_api_key($provided_key, $stored_key) {
        if (empty($provided_key) || empty($stored_key)) {
            return false;
        }

        return $provided_key === $stored_key;
    }

    /**
     * Log authentication debug information
     */
    private static function log_auth_debug($provided_key, $stored_key) {
        error_log('API Key Debug - Received: ' . ($provided_key ? substr($provided_key, 0, 10) . '...' : 'none') . ' | Stored: ' . ($stored_key ? substr($stored_key, 0, 10) . '...' : 'none'));
    }

    /**
     * Generate new API key
     */
    public static function generate_api_key() {
        return 'efm_' . bin2hex(random_bytes(16));
    }

    /**
     * Verify nonce for admin actions
     */
    public static function verify_nonce($nonce, $action) {
        if (!wp_verify_nonce($nonce, $action)) {
            wp_die('Security check failed');
        }
    }

    /**
     * Check user capabilities
     */
    public static function check_capabilities($capability = 'manage_options') {
        if (!current_user_can($capability)) {
            wp_die('You do not have sufficient permissions to access this page.');
        }
    }

    /**
     * Sanitize API key for storage
     */
    public static function sanitize_api_key($api_key) {
        return sanitize_text_field($api_key);
    }

    /**
     * Validate OpenAI API key access
     */
    public static function validate_openai_access() {
        $openai_key = get_option('elshadaifm_openai_key');

        if (empty($openai_key)) {
            return array(
                'success' => false,
                'message' => 'OpenAI API key not configured'
            );
        }

        return array(
            'success' => true,
            'message' => 'OpenAI API key is configured'
        );
    }
}