/* ==========================================================================
   ABOUT US PAGE JAVASCRIPT - MODULAR ARCHITECTURE
   ========================================================================== */

/**
 * About Us Main Controller
 * Initializes and coordinates all modular components
 *
 * Module Dependencies:
 * - about-base.js          (Base functionality & utilities)
 * - about-animations.js    (Animation classes & observers)
 * - about-timeline.js      (Timeline functionality)
 * - about-interactions.js  (Mouse interactions & hover effects)
 * - about-mobile.js        (Mobile-specific functionality)
 * - about-performance.js   (Performance optimizations)
 */
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    console.log('About Us: Initializing modular architecture...');

    // FORCE ALL SECTIONS TO BE VISIBLE FIRST
    console.log('About Us: Forcing all sections to be visible...');
    const allSections = document.querySelectorAll('.about-intro, .history-section, .vision-mission-section, .values-section, .stats-section, .cta-section');
    allSections.forEach(section => {
        section.style.opacity = '1';
        section.style.transform = 'translateY(0)';
        section.style.visibility = 'visible';
        section.style.display = 'block';
        console.log('Forced visible:', section.className);
    });

    // Initialize modules in order of dependency
    try {
        // 1. Base functionality first (required by other modules)
        if (window.AboutUsBase) {
            AboutUsBase.init();
        } else {
            console.error('AboutUsBase module not loaded');
        }

        // 2. Performance optimizations (should be early)
        if (window.AboutUsPerformance) {
            AboutUsPerformance.init();
        } else {
            console.warn('AboutUsPerformance module not loaded');
        }

        // 3. SKIP ANIMATIONS MODULE TO PREVENT HIDING
        console.warn('AboutUsAnimations module DISABLED to prevent section hiding');

        // 4. Timeline functionality (depends on base only)
        if (window.AboutUsTimeline) {
            AboutUsTimeline.init();
        } else {
            console.warn('AboutUsTimeline module not loaded');
        }

        // 5. Interactions and hover effects
        if (window.AboutUsInteractions) {
            AboutUsInteractions.init();
        } else {
            console.warn('AboutUsInteractions module not loaded');
        }

        // 6. Mobile-specific functionality (should be last)
        if (window.AboutUsMobile) {
            AboutUsMobile.init();
        } else {
            console.warn('AboutUsMobile module not loaded');
        }

        console.log('About Us: All modules initialized successfully');

        // Log module status
        console.group('About Us Module Status');
        console.log('Base:', !!window.AboutUsBase);
        console.log('Performance:', !!window.AboutUsPerformance);
        console.log('Animations:', !!window.AboutUsAnimations);
        console.log('Timeline:', !!window.AboutUsTimeline);
        console.log('Interactions:', !!window.AboutUsInteractions);
        console.log('Mobile:', !!window.AboutUsMobile);
        console.groupEnd();

    } catch (error) {
        console.error('About Us: Error during initialization:', error);

        // Fallback: Show all sections if modules fail
        console.log('About Us: Applying fallback visibility for all sections...');

        // Show timeline items
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
                item.classList.add('visible');
            }, index * 200);
        });

        // Show all sections
        const allSections = document.querySelectorAll('.vision-mission-section, .values-section, .stats-section, .cta-section');
        allSections.forEach((section, index) => {
            setTimeout(() => {
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
                console.log('Fallback: Showing section', section.className);
            }, index * 300);
        });
    }

    // Global error handler for runtime errors
    window.addEventListener('error', function(e) {
        console.error('About Us: Runtime error:', e.error);
    });

    // Cleanup on page unload
    window.addEventListener('beforeunload', function() {
        console.log('About Us: Cleaning up modules...');
        try {
            if (window.AboutUsPerformance && AboutUsPerformance.destroy) {
                AboutUsPerformance.destroy();
            }
            if (window.AboutUsAnimations && AboutUsAnimations.destroy) {
                AboutUsAnimations.destroy();
            }
            if (window.AboutUsTimeline && AboutUsTimeline.destroy) {
                AboutUsTimeline.destroy();
            }
            if (window.AboutUsInteractions && AboutUsInteractions.destroy) {
                AboutUsInteractions.destroy();
            }
            if (window.AboutUsMobile && AboutUsMobile.destroy) {
                AboutUsMobile.destroy();
            }
        } catch (error) {
            console.error('About Us: Error during cleanup:', error);
        }
    });

    // Development helper: Expose modules to global scope for debugging
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        window.AboutUsModules = {
            Base: window.AboutUsBase,
            Performance: window.AboutUsPerformance,
            Animations: window.AboutUsAnimations,
            Timeline: window.AboutUsTimeline,
            Interactions: window.AboutUsInteractions,
            Mobile: window.AboutUsMobile
        };
        console.log('About Us: Development mode - modules exposed to window.AboutUsModules');
    }
});

/*
 * Legacy support: If modules are not loaded, provide basic functionality
 */
window.addEventListener('load', function() {
    setTimeout(() => {
        // Check if timeline is still hidden after 2 seconds
        const hiddenItems = document.querySelectorAll('.timeline-item[style*="opacity: 0"]');
        if (hiddenItems.length > 0) {
            console.warn('About Us: Timeline items still hidden, applying emergency fallback...');
            hiddenItems.forEach((item, index) => {
                setTimeout(() => {
                    item.style.opacity = '1 !important';
                    item.style.transform = 'translateY(0) !important';
                    item.classList.add('visible');
                }, index * 100);
            });
        }

        // Check if sections are still hidden
        const hiddenSections = document.querySelectorAll('.vision-mission-section[style*="opacity: 0"], .values-section[style*="opacity: 0"], .stats-section[style*="opacity: 0"], .cta-section[style*="opacity: 0"]');
        if (hiddenSections.length > 0) {
            console.warn('About Us: Sections still hidden, applying emergency fallback...', hiddenSections.length);
            hiddenSections.forEach((section, index) => {
                setTimeout(() => {
                    section.style.opacity = '1 !important';
                    section.style.transform = 'translateY(0) !important';
                    console.log('Emergency fallback: Showing section', section.className);
                }, index * 150);
            });
        }
    }, 2000);
});