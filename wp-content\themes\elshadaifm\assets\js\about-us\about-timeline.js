/* ==========================================================================
   ABOUT US - TIMELINE FUNCTIONALITY
   ========================================================================== */

/**
 * About Us Timeline Module
 * Handles timeline animations, progress indicator, and interactions
 */
window.AboutUsTimeline = (function() {
    'use strict';

    // Private variables
    let isInitialized = false;
    let timelineItems = [];
    let progressIndicator = null;
    let timelineObserver = null;

    // Public API
    const api = {
        init: init,
        destroy: destroy,
        showItems: showItems,
        hideItems: hideItems
    };

    /**
     * Initialize timeline functionality
     */
    function init() {
        if (isInitialized) return;

        console.log('AboutUsTimeline: Initializing...');

        // Get timeline elements
        timelineItems = document.querySelectorAll('.timeline-item');
        console.log('Timeline items found:', timelineItems.length);

        if (timelineItems.length === 0) {
            console.warn('No timeline items found');
            return;
        }

        // Setup timeline animations
        setupTimelineAnimations();

        // Create progress indicator (desktop only)
        if (!AboutUsBase.isMobile()) {
            createTimelineProgress();
        }

        // Setup timeline interactions
        setupTimelineInteractions();

        isInitialized = true;
        console.log('AboutUsTimeline: Initialized successfully');
    }

    /**
     * Destroy timeline functionality
     */
    function destroy() {
        if (timelineObserver) {
            timelineObserver.disconnect();
            timelineObserver = null;
        }

        if (progressIndicator) {
            progressIndicator.remove();
            progressIndicator = null;
        }

        isInitialized = false;
    }

    /**
     * Setup timeline animations
     */
    function setupTimelineAnimations() {
        // Initialize timeline items
        timelineItems.forEach((item, index) => {
            // Set initial state only if CSS animation classes are supported
            if (window.IntersectionObserver) {
                item.style.opacity = '0';
                item.style.transform = 'translateY(60px)';
                item.style.transitionDelay = `${index * 0.15}s`;
            } else {
                // Fallback: show items immediately if no Intersection Observer
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
                item.classList.add('visible');
            }
        });

        // Create timeline observer with better threshold
        timelineObserver = new IntersectionObserver(function(entries) {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    // Add visible class for CSS animation
                    setTimeout(() => {
                        entry.target.classList.add('visible');
                    }, index * 100);

                    // Unobserve after animation to prevent re-triggering
                    timelineObserver.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.2,
            rootMargin: '0px 0px -50px 0px'
        });

        // Observe all timeline items
        timelineItems.forEach(item => timelineObserver.observe(item));

        // Fallback: Show timeline items after 3 seconds if they're still hidden
        setTimeout(() => {
            timelineItems.forEach((item, index) => {
                if (item.style.opacity === '0' || !item.classList.contains('visible')) {
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                        item.classList.add('visible');
                    }, index * 200);
                }
            });
        }, 3000);

        // Add timeline line animation
        const timelineSection = document.querySelector('.history-section');
        if (timelineSection) {
            const timelineSectionObserver = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        // Animate timeline line
                        const timeline = entry.target.querySelector('.history-timeline');
                        if (timeline) {
                            timeline.style.setProperty('--line-animation', 'timeline-line-draw 2s ease-out forwards');
                        }
                    }
                });
            }, { threshold: 0.1 });

            timelineSectionObserver.observe(timelineSection);
        }
    }

    /**
     * Create timeline progress indicator
     */
    function createTimelineProgress() {
        const historySection = document.querySelector('.history-section');
        if (!historySection || timelineItems.length === 0) return;

        // Create progress indicator HTML
        const progressHTML = `
            <div class="timeline-progress" id="timelineProgress">
                <div class="timeline-progress-bar">
                    <div class="timeline-progress-fill"></div>
                    <div class="timeline-progress-dots">
                        ${Array.from(timelineItems).map((_, index) => 
                            `<div class="timeline-progress-dot" data-timeline-index="${index}"></div>`
                        ).join('')}
                    </div>
                </div>
            </div>
        `;

        // Insert progress indicator
        document.body.insertAdjacentHTML('beforeend', progressHTML);

        progressIndicator = document.getElementById('timelineProgress');
        const progressFill = progressIndicator.querySelector('.timeline-progress-fill');
        const progressDots = progressIndicator.querySelectorAll('.timeline-progress-dot');

        // Show/hide progress indicator based on history section visibility
        const progressObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    progressIndicator.classList.add('visible');
                } else {
                    progressIndicator.classList.remove('visible');
                }
            });
        }, { threshold: 0.1 });

        progressObserver.observe(historySection);

        // Update progress based on scroll
        function updateTimelineProgress() {
            const sectionRect = historySection.getBoundingClientRect();
            const sectionTop = sectionRect.top + window.pageYOffset;
            const sectionHeight = sectionRect.height;
            const scrolled = window.pageYOffset;
            const windowHeight = window.innerHeight;

            // Calculate progress percentage
            const sectionStart = sectionTop - windowHeight / 2;
            const sectionEnd = sectionTop + sectionHeight - windowHeight / 2;
            const progress = Math.max(0, Math.min(1, (scrolled - sectionStart) / (sectionEnd - sectionStart)));

            // Update progress fill
            progressFill.style.height = `${progress * 100}%`;

            // Update active dots
            const activeIndex = Math.floor(progress * timelineItems.length);
            progressDots.forEach((dot, index) => {
                if (index <= activeIndex) {
                    dot.classList.add('active');
                } else {
                    dot.classList.remove('active');
                }
            });
        }

        // Add click handlers to progress dots
        progressDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                const targetItem = timelineItems[index];
                if (targetItem) {
                    targetItem.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            });
        });

        // Throttled scroll handler for progress
        let progressTimeout;
        window.addEventListener('scroll', () => {
            if (progressTimeout) {
                cancelAnimationFrame(progressTimeout);
            }
            progressTimeout = requestAnimationFrame(updateTimelineProgress);
        });
    }

    /**
     * Setup timeline interactions
     */
    function setupTimelineInteractions() {
        timelineItems.forEach((item, index) => {
            // Add data attributes for easier targeting
            item.setAttribute('data-timeline-index', index);

            // Enhanced hover effects
            item.addEventListener('mouseenter', function() {
                this.style.zIndex = '10';

                // Subtle scale effect for content
                const content = this.querySelector('.timeline-content');
                if (content) {
                    content.style.transform = 'translateY(-8px) scale(1.02)';
                }
            });

            item.addEventListener('mouseleave', function() {
                this.style.zIndex = '1';

                const content = this.querySelector('.timeline-content');
                if (content) {
                    content.style.transform = 'translateY(0) scale(1)';
                }
            });

            // Add click handler for mobile
            if (AboutUsBase.isMobile()) {
                item.addEventListener('click', function() {
                    const content = this.querySelector('.timeline-content');
                    if (content) {
                        content.classList.toggle('expanded');
                    }
                });
            }
        });
    }

    /**
     * Show all timeline items (utility function)
     */
    function showItems() {
        timelineItems.forEach(item => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
            item.classList.add('visible');
        });
    }

    /**
     * Hide all timeline items (utility function)
     */
    function hideItems() {
        timelineItems.forEach(item => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(60px)';
            item.classList.remove('visible');
        });
    }

    return api;
})();
