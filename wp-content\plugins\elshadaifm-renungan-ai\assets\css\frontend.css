/* Elshadaifm Renungan AI Frontend Styles */

/* Container Layout */
.efrai-container {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.efrai-main-content {
    min-width: 0;
}

/* Single Renungan Styles */
.efrai-renungan-single {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.efrai-renungan-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.efrai-renungan-title {
    font-size: 2.5em;
    margin: 0 0 20px 0;
    font-weight: 700;
    line-height: 1.2;
}

.efrai-renungan-meta {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.efrai-meta-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9em;
}

.efrai-meta-item .dashicons {
    font-size: 16px;
}

.efrai-renungan-featured-image {
    text-align: center;
    padding: 30px;
    background: #f8f9fa;
}

.efrai-featured-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.efrai-renungan-content {
    padding: 40px;
    line-height: 1.8;
    font-size: 1.1em;
    color: #333;
}

.efrai-renungan-content h2,
.efrai-renungan-content h3 {
    color: #667eea;
    margin-top: 30px;
    margin-bottom: 15px;
}

.efrai-renungan-content p {
    margin-bottom: 20px;
}

.efrai-generated-image-container {
    padding: 30px 40px;
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

.efrai-generated-image-container h3 {
    margin-top: 0;
    color: #667eea;
    text-align: center;
}

.efrai-generated-image {
    display: block;
    max-width: 100%;
    height: auto;
    margin: 20px auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.efrai-renungan-footer {
    padding: 30px 40px;
    background: #f8f9fa;
    border-top: 1px solid #eee;
}

.efrai-sharing {
    text-align: center;
    margin-bottom: 30px;
}

.efrai-sharing h4 {
    margin-bottom: 15px;
    color: #333;
}

.efrai-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
}

.efrai-nav-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 24px;
    background: #667eea;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.efrai-nav-link:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

/* Archive Styles */
.efrai-archive-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
}

.efrai-archive-title {
    font-size: 3em;
    margin: 0 0 15px 0;
    font-weight: 700;
}

.efrai-archive-description {
    font-size: 1.2em;
    opacity: 0.9;
    margin: 0;
}

.efrai-renungan-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.efrai-renungan-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.efrai-renungan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.efrai-card-image {
    height: 200px;
    overflow: hidden;
}

.efrai-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.efrai-renungan-card:hover .efrai-card-image img {
    transform: scale(1.05);
}

.efrai-card-content {
    padding: 25px;
}

.efrai-card-header {
    margin-bottom: 15px;
}

.efrai-card-title {
    font-size: 1.4em;
    margin: 0 0 10px 0;
    line-height: 1.3;
}

.efrai-card-title a {
    color: #333;
    text-decoration: none;
    font-weight: 600;
}

.efrai-card-title a:hover {
    color: #667eea;
}

.efrai-card-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 0.85em;
    color: #666;
}

.efrai-meta-date,
.efrai-meta-theme {
    display: flex;
    align-items: center;
    gap: 5px;
}

.efrai-card-excerpt {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.efrai-card-footer {
    text-align: right;
}

.efrai-read-more {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border: 2px solid #667eea;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.efrai-read-more:hover {
    background: #667eea;
    color: white;
}

/* Pagination */
.efrai-pagination {
    margin-top: 40px;
    text-align: center;
}

.efrai-pagination .page-numbers {
    display: inline-flex;
    gap: 5px;
    align-items: center;
}

.efrai-pagination .page-numbers a,
.efrai-pagination .page-numbers span {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-decoration: none;
    color: #667eea;
    transition: all 0.3s ease;
}

.efrai-pagination .page-numbers a:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.efrai-pagination .page-numbers .current {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Shortcode Styles */
.efrai-renungan-shortcode {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 20px;
}

.efrai-renungan-item {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
    padding: 30px;
}

.efrai-renungan-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

.efrai-renungan-content h3 {
    margin: 0 0 15px 0;
    font-size: 1.5em;
}

.efrai-renungan-content h3 a {
    color: #333;
    text-decoration: none;
}

.efrai-renungan-content h3 a:hover {
    color: #667eea;
}

.efrai-renungan-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.efrai-renungan-excerpt {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
}

.efrai-renungan-footer {
    text-align: right;
}

/* Latest Renungan Shortcode */
.efrai-latest-renungan {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

.efrai-latest-item {
    display: flex;
    gap: 20px;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.efrai-latest-item:last-child {
    border-bottom: none;
}

.efrai-latest-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 8px;
}

.efrai-latest-content {
    flex: 1;
}

.efrai-latest-title {
    margin: 0 0 10px 0;
    font-size: 1.2em;
}

.efrai-latest-title a {
    color: #333;
    text-decoration: none;
}

.efrai-latest-title a:hover {
    color: #667eea;
}

.efrai-latest-date {
    font-size: 0.9em;
    color: #666;
    margin-bottom: 8px;
}

.efrai-latest-excerpt {
    color: #666;
    font-size: 0.9em;
    line-height: 1.5;
}

.efrai-latest-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    text-align: center;
}

.efrai-view-all {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.efrai-view-all:hover {
    color: #5a6fd8;
}

/* No Content Styles */
.efrai-no-content {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.efrai-no-content h2 {
    color: #333;
    margin-bottom: 15px;
}

.efrai-no-content p {
    color: #666;
    margin-bottom: 30px;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .efrai-container {
        grid-template-columns: 1fr;
        padding: 15px;
    }

    .efrai-renungan-title {
        font-size: 2em;
    }

    .efrai-renungan-content {
        padding: 25px;
    }

    .efrai-renungan-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .efrai-renungan-item {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .efrai-navigation {
        flex-direction: column;
        gap: 15px;
    }

    .efrai-nav-link {
        justify-content: center;
        width: 100%;
    }

    .efrai-latest-item {
        flex-direction: column;
        text-align: center;
    }

    .efrai-latest-image {
        text-align: center;
    }
}