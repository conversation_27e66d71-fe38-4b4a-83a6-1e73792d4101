/* ==========================================================================
   ABOUT US - VALUES SECTION
   ========================================================================== */

/* Values Section */
.values-section {
    padding: 120px 0;
    background: white;
    position: relative;
    opacity: 1 !important; /* Always visible */
    transform: translateY(0) !important; /* Always in position */
}

.values-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.value-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.value-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #8B5CF6, #7C3AED);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.value-card:hover::before {
    transform: scaleX(1);
}

.value-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(139, 92, 246, 0.15);
}

.value-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.value-card:nth-child(1) .value-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.value-card:nth-child(2) .value-icon {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
}

.value-card:nth-child(3) .value-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.value-card:nth-child(4) .value-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.value-card:hover .value-icon {
    transform: scale(1.1) rotate(5deg);
}

.value-card h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 16px;
    color: #1e293b;
}

.value-card p {
    color: #64748b;
    line-height: 1.7;
    margin: 0;
    font-size: 1.1rem;
}
