<?php
/**
 * Logger Module for Elshadaifm Renungan API
 * Handles logging and debugging functionality
 */

class Elshadaifm_Renungan_Logger {

    /**
     * Log levels
     */
    const DEBUG = 'DEBUG';
    const INFO = 'INFO';
    const WARNING = 'WARNING';
    const ERROR = 'ERROR';
    const CRITICAL = 'CRITICAL';

    /**
     * Log API request
     */
    public static function log_api_request($endpoint, $method, $params = array()) {
        $message = sprintf(
            'API Request: %s %s | Params: %s',
            $method,
            $endpoint,
            json_encode($params)
        );
        self::log($message, self::INFO);
    }

    /**
     * Log API response
     * @param string $endpoint
     * @param int $status_code
     * @param array|WP_Error $response_data
     */
    public static function log_api_response($endpoint, $status_code, $response_data = array()) {
        $message = sprintf(
            'API Response: %s | Status: %d | Response: %s',
            $endpoint,
            $status_code,
            is_wp_error($response_data) ? $response_data->get_error_message() : json_encode($response_data)
        );
        self::log($message, self::INFO);
    }

    /**
     * Log OpenAI API call
     */
    public static function log_openai_call($endpoint, $model, $tokens_used = null) {
        $message = sprintf(
            'OpenAI Call: %s | Model: %s | Tokens: %s',
            $endpoint,
            $model,
            $tokens_used ? $tokens_used : 'N/A'
        );
        self::log($message, self::INFO);
    }

    /**
     * Log OpenAI API error
     */
    public static function log_openai_error($endpoint, $error_message, $status_code = null) {
        $message = sprintf(
            'OpenAI Error: %s | Status: %s | Error: %s',
            $endpoint,
            $status_code ? $status_code : 'Unknown',
            $error_message
        );
        self::log($message, self::ERROR);
    }

    /**
     * Log database operation
     */
    public static function log_database_operation($operation, $table, $result = null) {
        $message = sprintf(
            'Database Operation: %s on %s | Result: %s',
            $operation,
            $table,
            $result ? 'Success' : 'Failed'
        );
        self::log($message, self::DEBUG);
    }

    /**
     * Log image generation
     */
    public static function log_image_generation($prompt_length, $attachment_id, $generation_time) {
        $message = sprintf(
            'Image Generated: Prompt Length: %d chars | Attachment ID: %d | Generation Time: %d seconds',
            $prompt_length,
            $attachment_id,
            $generation_time
        );
        self::log($message, self::INFO);
    }

    /**
     * Log authentication attempt
     */
    public static function log_auth_attempt($key_prefix, $success) {
        $message = sprintf(
            'Auth Attempt: Key: %s... | Success: %s',
            $key_prefix,
            $success ? 'Yes' : 'No'
        );
        self::log($message, $success ? self::INFO : self::WARNING);
    }

    /**
     * Log content processing
     */
    public static function log_content_processing($content_id, $operation, $result = null) {
        $message = sprintf(
            'Content Processing: ID %d | Operation: %s | Result: %s',
            $content_id,
            $operation,
            $result ? 'Success' : 'Failed'
        );
        self::log($message, self::DEBUG);
    }

    /**
     * Log performance metrics
     */
    public static function log_performance($operation, $execution_time, $memory_usage = null) {
        $message = sprintf(
            'Performance: %s | Time: %.3f seconds | Memory: %s',
            $operation,
            $execution_time,
            $memory_usage ? self::format_bytes($memory_usage) : 'N/A'
        );
        self::log($message, self::DEBUG);
    }

    /**
     * Log error with context
     */
    public static function log_error($error_message, $context = array()) {
        $message = sprintf(
            'Error: %s | Context: %s',
            $error_message,
            json_encode($context)
        );
        self::log($message, self::ERROR);
    }

    /**
     * Log debug information
     */
    public static function log_debug($message, $context = array()) {
        if (WP_DEBUG) {
            $debug_message = sprintf(
                'Debug: %s | Context: %s',
                $message,
                json_encode($context)
            );
            self::log($debug_message, self::DEBUG);
        }
    }

    /**
     * Generic log method
     */
    public static function log($message, $level = self::INFO) {
        $timestamp = current_time('mysql');
        $log_message = sprintf(
            '[%s] [%s] %s',
            $timestamp,
            $level,
            $message
        );

        error_log($log_message);

        // Additional logging to custom log file if needed
        self::log_to_file($log_message);
    }

    /**
     * Log to custom file
     */
    private static function log_to_file($message) {
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/renungan-logs';

        if (!file_exists($log_dir)) {
            wp_mkdir_p($log_dir);
        }

        $log_file = $log_dir . '/api-' . date('Y-m-d') . '.log';
        $formatted_message = $message . PHP_EOL;

        file_put_contents($log_file, $formatted_message, FILE_APPEND | LOCK_EX);
    }

    /**
     * Format bytes to human readable format
     */
    private static function format_bytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    /**
     * Get log files list
     */
    public static function get_log_files() {
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/renungan-logs';

        if (!file_exists($log_dir)) {
            return array();
        }

        $files = glob($log_dir . '/*.log');
        $log_files = array();

        foreach ($files as $file) {
            $log_files[] = array(
                'filename' => basename($file),
                'path' => $file,
                'size' => filesize($file),
                'modified' => filemtime($file)
            );
        }

        return $log_files;
    }

    /**
     * Clean old log files
     */
    public static function clean_old_logs($days_to_keep = 30) {
        $upload_dir = wp_upload_dir();
        $log_dir = $upload_dir['basedir'] . '/renungan-logs';

        if (!file_exists($log_dir)) {
            return true;
        }

        $files = glob($log_dir . '/*.log');
        $cutoff_time = time() - ($days_to_keep * 24 * 60 * 60);
        $deleted_count = 0;

        foreach ($files as $file) {
            if (filemtime($file) < $cutoff_time) {
                unlink($file);
                $deleted_count++;
            }
        }

        return $deleted_count;
    }
}