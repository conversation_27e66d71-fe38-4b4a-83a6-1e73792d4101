<div class="wrap">
    <h1><?php echo esc_html__('Generate Renungan with AI', 'elshadaifm-renungan-ai'); ?></h1>

    <div class="efrai-generate-container">
        <div class="efrai-section">
            <h2><?php echo esc_html__('Input Renungan Content', 'elshadaifm-renungan-ai'); ?></h2>
            <p><?php echo esc_html__('Paste or type the renungan content below. The AI will extract the date and generate a complete renungan post.', 'elshadaifm-renungan-ai'); ?></p>

            <div class="efrai-form-group">
                <label for="efrai_content_input"><?php echo esc_html__('Renungan Content:', 'elshadaifm-renungan-ai'); ?></label>
                <textarea id="efrai_content_input" class="efrai-textarea" rows="15" placeholder="<?php echo esc_html__('Paste renungan content here...

Example format:
RENUNGAN KELUARGA ALLAH
BIMBINGAN ROHANI – Take Over Mode #3
22-28 September 2025

SENIN, 22 SEPTEMBER 2025
Sadari dimensi ROH itu NYATA, maka engkau akan MENANG.
1 Yohanes 5:4 Sebab semua yang lahir dari Allah, mengalahkan dunia. Dan inilah kemenangan yang mengalahkan dunia: iman kita.

SIANG
Iman yang BERKOBAR-KOBAR akan membawamu dari KEMENANGAN kepada kemenangan.
1 Korintus 15:57 Tetapi syukur kepada Allah, yang telah memberikan kepada kita kemenangan oleh Yesus Kristus, Tuhan kita.

MALAM
Masalah yang tampak sering BERAKAR dari PEPERANGAN ROHANI yang tak tampak.
Efesus 6:12 Karena perjuangan kita bukanlah melawan darah dan daging, tetapi melawan pemerintah-pemerintah, melawan penguasa-penguasa dunia yang gelap ini, melawan roh-roh jahat di udara.', 'elshadaifm-renungan-ai'); ?>"></textarea>
            </div>
        </div>

        <div class="efrai-section">
            <h2><?php echo esc_html__('AI-Generated Content', 'elshadaifm-renungan-ai'); ?></h2>

            <button id="efrai_extract_btn" class="button button-primary">
                <span class="dashicons dashicons-artificial-intelligence"></span>
                <?php echo esc_html__('Process with AI', 'elshadaifm-renungan-ai'); ?>
            </button>

            <div class="efrai-form-group">
                <label><?php echo esc_html__('Extracted Dates:', 'elshadaifm-renungan-ai'); ?></label>
                <div id="efrai_extracted_dates" class="efrai-dates-list">
                    <div class="efrai-no-dates">
                        <span class="dashicons dashicons-clock"></span>
                        <?php echo esc_html__('Click "Extract Dates" to find dates in your content', 'elshadaifm-renungan-ai'); ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="efrai-section" id="efrai_result_section" style="display: none;">
            <h2><?php echo esc_html__('Generated Result', 'elshadaifm-renungan-ai'); ?></h2>

            <div class="efrai-result-preview">
                <h3 id="efrai_result_title"></h3>
                <div class="efrai-result-meta">
                    <strong><?php echo esc_html__('Date:', 'elshadaifm-renungan-ai'); ?></strong> <span id="efrai_result_date"></span><br>
                    <strong><?php echo esc_html__('Verse:', 'elshadaifm-renungan-ai'); ?></strong> <span id="efrai_result_verse"></span><br>
                    <strong><?php echo esc_html__('Theme:', 'elshadaifm-renungan-ai'); ?></strong> <span id="efrai_result_theme"></span>
                </div>
                <div class="efrai-result-content">
                    <h4><?php echo esc_html__('Content:', 'elshadaifm-renungan-ai'); ?></h4>
                    <div id="efrai_result_content"></div>
                </div>
            </div>

            <div class="efrai-result-actions">
                <button id="efrai_save_renungan" class="button button-primary">
                    <?php echo esc_html__('Save as Post', 'elshadaifm-renungan-ai'); ?>
                </button>
                <button id="efrai_generate_image" class="button">
                    <?php echo esc_html__('Generate Image', 'elshadaifm-renungan-ai'); ?>
                </button>
                <button id="efrai_regenerate" class="button">
                    <?php echo esc_html__('Regenerate', 'elshadaifm-renungan-ai'); ?>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.efrai-generate-container {
    max-width: 800px;
    margin: 20px 0;
}

.efrai-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.efrai-upload-area {
    border: 2px dashed #b4b9be;
    border-radius: 4px;
    padding: 40px;
    text-align: center;
    margin: 20px 0;
}

.efrai-file-input {
    display: none;
}

.efrai-upload-label {
    display: inline-block;
    padding: 10px 20px;
    background: #f8f9f9;
    border: 1px solid #b4b9be;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.efrai-upload-label:hover {
    background: #e9e9e9;
    border-color: #0073aa;
}

.efrai-file-info {
    margin-top: 10px;
    padding: 10px;
    background: #f0f6fc;
    border: 1px solid #4f94d4;
    border-radius: 4px;
}

.efrai-form-group {
    margin-bottom: 15px;
}

.efrai-form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.efrai-date-input,
.efrai-textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #b4b9be;
    border-radius: 4px;
}

.efrai-textarea {
    resize: vertical;
}

.efrai-result-preview {
    background: #f8f9f9;
    border: 1px solid #b4b9be;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.efrai-result-meta {
    margin: 10px 0;
    padding: 10px;
    background: #fff;
    border-left: 4px solid #0073aa;
}

.efrai-result-content {
    margin-top: 15px;
}

.efrai-result-actions {
    margin-top: 20px;
}

.efrai-result-actions .button {
    margin-right: 10px;
}

.efrai-check-result {
    padding: 10px;
    border-radius: 4px;
    margin-top: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.efrai-check-result.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.efrai-check-result.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.efrai-check-result.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.efrai-check-result.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.dashicons-spinner {
    animation: spin 1s linear infinite;
    display: none;
}

.button:disabled .dashicons-spinner {
    display: inline-block;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.efrai-dates-list {
    margin-top: 10px;
}

.efrai-date-item {
    background: #f8f9f9;
    border: 1px solid #b4b9be;
    border-radius: 4px;
    margin-bottom: 10px;
    overflow: hidden;
}

.efrai-date-header {
    padding: 10px 15px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.2s;
}

.efrai-date-header:hover {
    background: #e9e9e9;
}

.efrai-date-header.has-existing {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
}

.efrai-date-header.available {
    background: #d4edda;
    border-left: 4px solid #28a745;
}

.efrai-date-info {
    display: flex;
    gap: 15px;
    align-items: center;
}

.efrai-date-actions {
    display: flex;
    gap: 5px;
}

.efrai-date-details {
    padding: 0 15px;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #fff;
}

.efrai-date-details.expanded {
    max-height: 300px;
    padding: 15px;
}

.efrai-date-preview {
    background: #f1f1f1;
    padding: 10px;
    border-radius: 4px;
    margin-top: 10px;
    font-size: 13px;
    line-height: 1.4;
}

.efrai-date-status {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
}

.efrai-date-status.existing {
    background: #dc3545;
    color: white;
}

.efrai-date-status.available {
    background: #28a745;
    color: white;
}

.efrai-no-dates {
    text-align: center;
    color: #666;
    padding: 20px;
    background: #f8f9f9;
    border: 1px dashed #b4b9be;
    border-radius: 4px;
}

.efrai-loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.efrai-loading .dashicons-spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
}

.efrai-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
}
</style>