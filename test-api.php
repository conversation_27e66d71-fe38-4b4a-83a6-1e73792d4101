<?php
// Test script untuk API Renungan dengan Form Data
// Jalankan via browser: http://localhost:8080/elshadaifm/test-api.php

// Ambil API key dari database
require_once 'wp-config.php';
$api_key = get_option('elshadaifm_api_key');
$openai_key = get_option('elshadaifm_openai_key');

// Sample content untuk test
$sample_content = "RENUNGAN KELUARGA ALLAH
BIMBINGAN ROHANI – Take Over Mode #3
22-28 September 2025
SENIN, 22 SEPTEMBER 2025
Sadari dimensi ROH itu NYATA, maka engkau akan MENANG.
1 Yohanes 5:4 Sebab semua yang lahir dari <PERSON>, mengalahkan dunia. Dan inilah kemenangan yang
mengalahkan dunia: iman kita.
SIANG
Iman yang BERKOBAR-KOBAR akan membawamu dari KEMENANGAN kepada kemenangan.
1 Korintus 15:57 Tetapi syukur kep<PERSON>, yang telah memberikan kepada kita kemenangan oleh <PERSON>, <PERSON><PERSON> kita.
MALAM
Masalah yang tampak sering BERAKAR dari PEPERANGAN ROHANI yang tak tampak.
Efesus 6:12 Karena perjuangan kita bukanlah melawan darah dan daging, tetapi melawan pemerintah-
pemerintah, melawan penguasa-penguasa dunia yang gelap ini, melawan roh-roh jahat di udara.";

$short_content = "RENUNGAN TEST
Ini adalah test singkat
1 Yohanes 3:16 Karena begitu besar kasih Allah...";

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test API Renungan</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 2px; }
        .form-group { margin: 10px 0; }
        label { display: block; font-weight: bold; margin-bottom: 5px; }
        textarea, input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>Test API Renungan Elshadaifm</h1>

    <div class="test-section info">
        <h3>API Keys</h3>
        <p><strong>Renungan API Key:</strong> <code><?php echo htmlspecialchars($api_key); ?></code></p>
        <p><strong>OpenAI API Key:</strong> <code><?php echo $openai_key ? substr(htmlspecialchars($openai_key), 0, 20) . '...' : 'Not set'; ?></code></p>
        <p><strong>Submit Endpoint:</strong> <code>POST <?php echo rest_url('elshadaifm-renungan/v1/submit'); ?></code></p>
        <p><strong>Generate Details Endpoint:</strong> <code>POST <?php echo rest_url('elshadaifm-renungan/v1/generate-details'); ?></code></p>
    </div>

    <div class="test-section">
        <h3>Test 1: Form Data Submit</h3>
        <?php
        $ch1 = curl_init();
        $data1 = array(
            'content' => $short_content,
            'api_key' => $api_key
        );
        curl_setopt_array($ch1, array(
            CURLOPT_URL => 'http://localhost:8080/elshadaifm/index.php?rest_route=/elshadaifm-renungan/v1/submit',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data1),
            CURLOPT_HTTPHEADER => array('Content-Type: application/x-www-form-urlencoded'),
            CURLOPT_SSL_VERIFYPEER => false
        ));
        $response1 = curl_exec($ch1);
        $http_code1 = curl_getinfo($ch1, CURLINFO_HTTP_CODE);
        curl_close($ch1);

        echo "<p><strong>Status:</strong> $http_code1</p>";
        echo "<pre>" . htmlspecialchars($response1) . "</pre>";
        ?>
    </div>

    <div class="test-section">
        <h3>Test 2: Duplicate Prevention</h3>
        <?php
        $ch2 = curl_init();
        $data2 = array(
            'content' => $short_content,
            'api_key' => $api_key
        );
        curl_setopt_array($ch2, array(
            CURLOPT_URL => 'http://localhost:8080/elshadaifm/index.php?rest_route=/elshadaifm-renungan/v1/submit',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data2),
            CURLOPT_HTTPHEADER => array('Content-Type: application/x-www-form-urlencoded'),
            CURLOPT_SSL_VERIFYPEER => false
        ));
        $response2 = curl_exec($ch2);
        $http_code2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
        curl_close($ch2);

        echo "<p><strong>Status:</strong> $http_code2";
        if ($http_code2 == 409) {
            echo " <span style='color: green;'>✅ Duplicate prevention works!</span>";
        }
        echo "</p>";
        echo "<pre>" . htmlspecialchars($response2) . "</pre>";
        ?>
    </div>

    <div class="test-section">
        <h3>Test 3: JSON Submit</h3>
        <?php
        $ch3 = curl_init();
        $data3 = array(
            'content' => $short_content . " (JSON version)",
            'api_key' => $api_key
        );
        curl_setopt_array($ch3, array(
            CURLOPT_URL => 'http://localhost:8080/elshadaifm/index.php?rest_route=/elshadaifm-renungan/v1/submit',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data3),
            CURLOPT_HTTPHEADER => array('Content-Type: application/json'),
            CURLOPT_SSL_VERIFYPEER => false
        ));
        $response3 = curl_exec($ch3);
        $http_code3 = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
        curl_close($ch3);

        echo "<p><strong>Status:</strong> $http_code3</p>";
        echo "<pre>" . htmlspecialchars($response3) . "</pre>";
        ?>
    </div>

    <div class="test-section">
        <h3>Test 4: Full Content Submit</h3>
        <?php
        $ch4 = curl_init();
        $data4 = array(
            'content' => $sample_content,
            'api_key' => $api_key
        );
        curl_setopt_array($ch4, array(
            CURLOPT_URL => 'http://localhost:8080/elshadaifm/index.php?rest_route=/elshadaifm-renungan/v1/submit',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => http_build_query($data4),
            CURLOPT_HTTPHEADER => array('Content-Type: application/x-www-form-urlencoded'),
            CURLOPT_SSL_VERIFYPEER => false
        ));
        $response4 = curl_exec($ch4);
        $http_code4 = curl_getinfo($ch4, CURLINFO_HTTP_CODE);
        curl_close($ch4);

        echo "<p><strong>Status:</strong> $http_code4</p>";
        echo "<pre>" . htmlspecialchars($response4) . "</pre>";
        ?>
    </div>

    <div class="test-section">
        <h3>Database Status - efr_renungan</h3>
        <?php
        global $wpdb;
        $table_name = $wpdb->prefix . 'efr_renungan';
        $results = $wpdb->get_results("SELECT id, LEFT(content_hash, 10) as hash_prefix, status, created_at FROM $table_name ORDER BY id DESC LIMIT 5");

        if ($results) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Hash Prefix</th><th>Status</th><th>Created At</th></tr>";
            foreach ($results as $row) {
                echo "<tr>";
                echo "<td>" . $row->id . "</td>";
                echo "<td>" . $row->hash_prefix . "...</td>";
                echo "<td>" . $row->status . "</td>";
                echo "<td>" . $row->created_at . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No records found</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h3>Database Status - efr_renungan_detail</h3>
        <?php
        $detail_table_name = $wpdb->prefix . 'efr_renungan_detail';
        $detail_results = $wpdb->get_results("SELECT id, tanggal_publish, ayat, judul, LEFT(summary, 50) as summary_preview, created FROM $detail_table_name ORDER BY id DESC LIMIT 5");

        if ($detail_results) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Tanggal Publish</th><th>Ayat</th><th>Judul</th><th>Summary</th><th>Created</th></tr>";
            foreach ($detail_results as $row) {
                echo "<tr>";
                echo "<td>" . $row->id . "</td>";
                echo "<td>" . ($row->tanggal_publish ?: '-') . "</td>";
                echo "<td>" . ($row->ayat ? substr($row->ayat, 0, 30) . '...' : '-') . "</td>";
                echo "<td>" . ($row->judul ? substr($row->judul, 0, 30) . '...' : '-') . "</td>";
                echo "<td>" . ($row->summary_preview ? $row->summary_preview . '...' : '-') . "</td>";
                echo "<td>" . $row->created . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No detailed records found</p>";
        }
        ?>
    </div>

    <div class="test-section">
        <h3>Interactive Test Form</h3>
        <form id="testForm">
            <div class="form-group">
                <label for="testContent">Content:</label>
                <textarea id="testContent" rows="10" placeholder="Paste renungan content here..."><?php echo htmlspecialchars($short_content); ?></textarea>
            </div>
            <div class="form-group">
                <label for="testApiKey">API Key:</label>
                <input type="text" id="testApiKey" value="<?php echo htmlspecialchars($api_key); ?>">
            </div>
            <button type="submit">Test Submit</button>
        </form>
        <div id="testResult" style="margin-top: 15px;"></div>
    </div>

    <div class="test-section">
        <h3>Test Generate Details</h3>
        <button onclick="testGenerateDetails()" <?php echo !$openai_key ? 'disabled title="OpenAI API key not set"' : ''; ?>>
            Test Generate Details
        </button>
        <div id="generateResult" style="margin-top: 15px;"></div>
    </div>

    <script>
    document.getElementById('testForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const content = document.getElementById('testContent').value;
        const apiKey = document.getElementById('testApiKey').value;
        const resultDiv = document.getElementById('testResult');

        const formData = new FormData();
        formData.append('content', content);
        formData.append('api_key', apiKey);

        resultDiv.innerHTML = '<p>Testing...</p>';

        fetch('http://localhost:8080/elshadaifm/index.php?rest_route=/elshadaifm-renungan/v1/submit', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            const status = data.success ? 'success' : 'error';
            resultDiv.innerHTML = `
                <div class="test-section ${status}">
                    <h4>Result:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        })
        .catch(error => {
            resultDiv.innerHTML = `
                <div class="test-section error">
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                </div>
            `;
        });
    });
    function testGenerateDetails() {
        const resultDiv = document.getElementById('generateResult');
        resultDiv.innerHTML = '<p>Generating details...</p>';

        const formData = new FormData();
        formData.append('api_key', '<?php echo $api_key; ?>');

        fetch('http://localhost:8080/elshadaifm/index.php?rest_route=/elshadaifm-renungan/v1/generate-details', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            const status = data.success ? 'success' : 'error';
            resultDiv.innerHTML = `
                <div class="test-section ${status}">
                    <h4>Result:</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        })
        .catch(error => {
            resultDiv.innerHTML = `
                <div class="test-section error">
                    <h4>Error:</h4>
                    <pre>${error.message}</pre>
                </div>
            `;
        });
    }
    </script>
</body>
</html>