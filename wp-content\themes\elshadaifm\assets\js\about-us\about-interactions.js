/* ==========================================================================
   ABOUT US - INTERACTIONS & HOVER EFFECTS
   ========================================================================== */

/**
 * About Us Interactions Module
 * Handles mouse interactions, hover effects, and user interactions
 */
window.AboutUsInteractions = (function() {
    'use strict';

    // Private variables
    let isInitialized = false;

    // Public API
    const api = {
        init: init,
        destroy: destroy
    };

    /**
     * Initialize interactions
     */
    function init() {
        if (isInitialized) return;

        console.log('AboutUsInteractions: Initializing...');

        // Setup card interactions
        setupCardInteractions();

        // Setup button interactions
        setupButtonInteractions();

        // Setup value card interactions
        setupValueCardInteractions();

        isInitialized = true;
        console.log('AboutUsInteractions: Initialized successfully');
    }

    /**
     * Destroy interactions
     */
    function destroy() {
        // Remove event listeners if needed
        isInitialized = false;
    }

    /**
     * Setup card mouse move effects
     */
    function setupCardInteractions() {
        document.querySelectorAll('.highlight-card, .value-card').forEach(card => {
            card.addEventListener('mousemove', (e) => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                const rotateX = (y - centerY) / 20;
                const rotateY = (centerX - x) / 20;

                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
            });
        });
    }

    /**
     * Setup button interactions
     */
    function setupButtonInteractions() {
        // Add ripple effect to CTA button
        const ctaButton = document.querySelector('.cta-button');
        if (ctaButton) {
            ctaButton.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        }
    }

    /**
     * Setup value card interactions
     */
    function setupValueCardInteractions() {
        const valueCards = document.querySelectorAll('.value-card');
        valueCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                const icon = this.querySelector('.value-icon');
                if (icon) {
                    icon.style.transform = 'scale(1.2) rotate(360deg)';
                }
            });

            card.addEventListener('mouseleave', function() {
                const icon = this.querySelector('.value-icon');
                if (icon) {
                    icon.style.transform = 'scale(1) rotate(0deg)';
                }
            });
        });
    }

    /**
     * Setup focus interactions for accessibility
     */
    function setupFocusInteractions() {
        // Add focus handling for timeline items
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach(item => {
            item.addEventListener('focus', function() {
                this.style.outline = '2px solid #8B5CF6';
                this.style.outlineOffset = '4px';
                this.style.borderRadius = '8px';

                const content = this.querySelector('.timeline-content');
                if (content) {
                    content.style.transform = 'translateY(-5px) scale(1.02)';
                    content.style.boxShadow = '0 20px 60px rgba(139, 92, 246, 0.2)';
                }
            });

            item.addEventListener('blur', function() {
                this.style.outline = '';
                this.style.outlineOffset = '';
                this.style.borderRadius = '';

                const content = this.querySelector('.timeline-content');
                if (content) {
                    content.style.transform = '';
                    content.style.boxShadow = '';
                }
            });
        });

        // Add focus handling for timeline dates
        const timelineDates = document.querySelectorAll('.timeline-date');
        timelineDates.forEach(date => {
            date.addEventListener('focus', function() {
                this.style.outline = '2px solid #ffffff';
                this.style.outlineOffset = '2px';
            });

            date.addEventListener('blur', function() {
                this.style.outline = '';
                this.style.outlineOffset = '';
            });
        });
    }

    /**
     * Setup touch interactions for mobile
     */
    function setupTouchInteractions() {
        if (!AboutUsBase.isMobile()) return;

        // Add touch feedback for cards
        document.querySelectorAll('.highlight-card, .value-card, .timeline-item').forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });

            element.addEventListener('touchend', function() {
                this.style.transform = '';
            });

            element.addEventListener('touchcancel', function() {
                this.style.transform = '';
            });
        });
    }

    /**
     * Setup drag interactions (if needed)
     */
    function setupDragInteractions() {
        // Prevent dragging of images and other elements
        document.querySelectorAll('img, .timeline-date, .highlight-icon').forEach(element => {
            element.addEventListener('dragstart', function(e) {
                e.preventDefault();
            });
        });
    }

    /**
     * Setup custom cursor effects
     */
    function setupCursorEffects() {
        // Add custom cursor for interactive elements
        document.querySelectorAll('.timeline-date, .cta-button, .timeline-progress-dot').forEach(element => {
            element.style.cursor = 'pointer';
        });

        // Add grab cursor for draggable elements (if any)
        document.querySelectorAll('.draggable').forEach(element => {
            element.style.cursor = 'grab';

            element.addEventListener('mousedown', function() {
                this.style.cursor = 'grabbing';
            });

            element.addEventListener('mouseup', function() {
                this.style.cursor = 'grab';
            });
        });
    }

    // Initialize additional interactions
    setTimeout(() => {
        setupFocusInteractions();
        setupTouchInteractions();
        setupDragInteractions();
        setupCursorEffects();
    }, 100);

    return api;
})();
