/*
 * Broadcast Schedule Section Styles
 * ElshadaiFM Theme - Bold Radio Station Aesthetic
 * Design Philosophy: Sharp corners, white text, adaptive backgrounds, 7-day navigation
 */

/* Base Section Styling */
.broadcast-schedule-section {
    position: relative;
    background: #ffffff;
    padding: 50px 0;
    overflow: hidden;
}

/* Animated Radio Wave Background */
.schedule-background-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.radio-wave {
    position: absolute;
    border: 2px solid;
    border-radius: 50%;
    opacity: 0.1;
    animation: radioWavePulse 4s ease-in-out infinite;
}

.radio-wave.wave-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: -150px;
    border-color: #ff0040;
    animation-delay: 0s;
}

.radio-wave.wave-2 {
    width: 500px;
    height: 500px;
    bottom: -200px;
    left: -250px;
    border-color: #00ffff;
    animation-delay: 1.5s;
}

.radio-wave.wave-3 {
    width: 200px;
    height: 200px;
    top: 50%;
    left: 20%;
    border-color: #ffff00;
    animation-delay: 3s;
}

@keyframes radioWavePulse {
    0%, 100% { 
        transform: scale(0.8);
        opacity: 0.1;
    }
    50% { 
        transform: scale(1.2);
        opacity: 0.3;
    }
}

/* Container */
.broadcast-schedule-section .container {
    position: relative;
    z-index: 10;
    width: 90%;
    margin: 0 auto;
    padding: 0 20px;
}

/* Section Header */
.schedule-header {
    text-align: center;
    margin-bottom: 60px;
}

.schedule-title-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.schedule-main-title {
    font-size: 4.5rem;
    font-weight: 900;
    color: #333333;
    margin: 0;
    font-family: 'Poppins', 'Arial Black', sans-serif;
    text-transform: uppercase;
    letter-spacing: 3px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.title-accent {
    background: linear-gradient(101deg, rgba(24, 16, 123, 1) 0%, rgba(165, 32, 119, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.live-pulse {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 0, 64, 0.2);
    padding: 12px 20px;
    border: 2px solid #ff0040;
    backdrop-filter: blur(10px);
}

.pulse-dot {
    width: 12px;
    height: 12px;
    background: #ff0040;
    animation: livePulse 1.5s ease-in-out infinite;
    box-shadow: 0 0 15px #ff0040;
}

.pulse-text {
    color: #ff0040;
    font-weight: 700;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

@keyframes livePulse {
    0%, 100% { 
        transform: scale(1);
        opacity: 1;
    }
    50% { 
        transform: scale(1.3);
        opacity: 0.7;
    }
}

.schedule-subtitle {
    font-size: 1.3rem;
    color: #cccccc;
    font-weight: 300;
    margin: 0 auto;
    line-height: 1.6;
}

/* NOW PLAYING Section */
.now-playing-section {
    margin-bottom: 80px;
}

.now-playing-card {
    background: linear-gradient(135deg, rgba(255, 0, 64, 0.1), rgba(0, 255, 255, 0.05));
    border: 2px solid rgba(255, 0, 64, 0.3);
    padding: 40px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 40px;
    align-items: center;
}

.now-playing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: cardShimmer 3s ease-in-out infinite;
}

@keyframes cardShimmer {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

@keyframes liveGlow {
    0%, 100% {
        opacity: 0.8;
        box-shadow: 0 0 10px rgba(255, 0, 64, 0.5);
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 20px rgba(255, 0, 64, 0.8);
    }
}

@keyframes liveBadgePulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 4px 15px rgba(255, 0, 64, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 6px 20px rgba(255, 0, 64, 0.6),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
}

.now-playing-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    color: #ff0040;
    font-weight: 700;
    font-size: 1.1rem;
    letter-spacing: 1.5px;
}

.live-dot {
    width: 15px;
    height: 15px;
    background: #ff0040;
    animation: livePulse 1s ease-in-out infinite;
    box-shadow: 0 0 20px #ff0040;
}

.current-show-title {
    font-size: 2.8rem;
    font-weight: 800;
    color: #ffffff;
    margin: 0 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.current-show-host {
    font-size: 1.4rem;
    color: #00ffff;
    margin: 0 0 15px 0;
    font-weight: 500;
}

.current-show-time {
    font-size: 1.6rem;
    color: #ffff00;
    font-weight: 700;
    margin: 0 0 15px 0;
    font-family: 'Courier New', monospace;
}

.current-show-description {
    font-size: 1.1rem;
    color: #ffffff;
    line-height: 1.6;
    margin: 0;
}

/* Audio Visualizer */
.now-playing-visual {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.audio-visualizer {
    display: flex;
    align-items: end;
    gap: 4px;
    height: 60px;
}

.audio-visualizer .bar {
    width: 6px;
    background: linear-gradient(to top, #ff0040, #00ffff, #ffff00);
    animation: audioWave 1.5s ease-in-out infinite;
}

.audio-visualizer .bar:nth-child(1) { animation-delay: 0s; }
.audio-visualizer .bar:nth-child(2) { animation-delay: 0.2s; }
.audio-visualizer .bar:nth-child(3) { animation-delay: 0.4s; }
.audio-visualizer .bar:nth-child(4) { animation-delay: 0.6s; }
.audio-visualizer .bar:nth-child(5) { animation-delay: 0.8s; }
.audio-visualizer .bar:nth-child(6) { animation-delay: 1s; }

@keyframes audioWave {
    0%, 100% { 
        height: 20px;
        opacity: 0.6;
    }
    50% { 
        height: 60px;
        opacity: 1;
    }
}

.frequency-display {
    font-size: 2rem;
    font-weight: 900;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.3);
}

/* Schedule Carousel Section */
.schedule-carousel-section {
    margin-bottom: 60px;
}

.upcoming-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #333333;
    text-align: center;
    margin-bottom: 50px;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.upcoming-accent {
    background: linear-gradient(101deg, rgba(24, 16, 123, 1) 0%, rgba(165, 32, 119, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 7-Day Navigation */
.carousel-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 40px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
}

.nav-arrow {
    background: transparent;
    border: 2px solid #ffffff;
    color: #ffffff;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.5rem;
}

.nav-arrow:hover {
    border-color: #ff0040;
    color: #ff0040;
    background: rgba(255, 0, 64, 0.1);
    transform: scale(1.1);
}

.day-indicators {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.day-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 12px 16px;
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.day-indicator.active {
    border-color: #ff0040;
    background: rgba(255, 0, 64, 0.1);
    color: #ff0040;
}

.day-indicator:hover {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

.day-name {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
}

.day-date {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Schedule Carousel Container */
.schedule-carousel-container {
    position: relative;
    overflow: hidden;
}

.schedule-carousel {
    display: flex;
    transition: transform 0.4s ease;
}

.day-schedule {
    width: 100%;
    flex-shrink: 0;
    display: none;
}

.day-schedule.active {
    display: block;
}

/* Show Cards */
/* Shows Container */
.shows-container-outer {
    position: relative;
    width: 100%;
    margin: 20px 0;
}

.shows-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    margin: 0;
}

/* Container wrapper to limit visible area */
.shows-wrapper {
    position: relative;
    width: 100%; /* Full percentage width */
    height: 450px; /* Fixed height to contain cards */
    overflow: hidden;
    margin: 0 auto; /* Center the container */
    box-sizing: border-box;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .shows-wrapper {
        height: 420px;
    }
}

@media (max-width: 768px) {
    .shows-wrapper {
        height: 400px;
    }

    .shows-container-outer {
        margin: 15px 0;
    }

    .schedule-arrow {
        width: 40px;
        height: 40px;
    }

    .schedule-arrow.prev-arrow {
        left: -30px;
    }

    .schedule-arrow.next-arrow {
        right: -30px;
    }

    .show-card {
        width: 260px;
        padding: 18px;
    }
}

@media (max-width: 480px) {
    .shows-wrapper {
        height: 350px;
    }

    .shows-container-outer {
        margin: 10px 0;
    }

    .schedule-arrow {
        width: 35px;
        height: 35px;
    }

    .schedule-arrow.prev-arrow {
        left: -25px;
    }

    .schedule-arrow.next-arrow {
        right: -25px;
    }

    .show-card {
        width: 240px;
        padding: 15px;
    }

    .shows-grid {
        gap: 10px;
        padding: 5px 25px;
    }
}

/* Inner scrollable container */
.shows-scroll-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* Inner scrollable container */
.shows-scroll-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

/* Scrollable Shows Grid */
.shows-grid {
    display: flex;
    gap: 30px;
    padding: 30px 50px; /* Space for arrows */
    width: max-content; /* Allow content to determine width */
    box-sizing: border-box;
}

/* Navigation Arrows */
.schedule-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #ff0040, #a52077);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 20;
    box-shadow: 0 4px 15px rgba(255, 0, 64, 0.3);
}

.schedule-arrow:hover {
    background: linear-gradient(135deg, #ff0040, #a52077);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 0, 64, 0.4);
}

.schedule-arrow.prev-arrow {
    left: -40px;
}

.schedule-arrow.next-arrow {
    right: -40px;
}

.schedule-arrow:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: translateY(-50%) scale(0.8);
}

.show-card {
    background: linear-gradient(135deg, #18107b 0%, #a52077 100%);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 28px;
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 12px 40px rgba(24, 16, 123, 0.4),
        0 8px 20px rgba(165, 32, 119, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    cursor: pointer;
    width: 320px;
    flex-shrink: 0;
    backdrop-filter: blur(10px);
}

.show-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    z-index: 0;
    opacity: 0.3;
}

.show-card:hover {
    transform: translateY(-12px) scale(1.03);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 20px 48px rgba(24, 16, 123, 0.4),
        0 12px 24px rgba(165, 32, 119, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.show-card:hover::before {
    opacity: 0.5;
}

.show-card.live {
    border-color: rgba(255, 0, 64, 0.6);
    background: linear-gradient(135deg, #ff0040 0%, #a52077 100%);
    box-shadow:
        0 15px 50px rgba(255, 0, 64, 0.5),
        0 8px 25px rgba(165, 32, 119, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.show-card.live::before {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.2) 100%);
    height: 100%;
    animation: liveGlow 2s ease-in-out infinite;
}

.show-card.upcoming {
    border-color: rgba(24, 16, 123, 0.4);
    background: linear-gradient(135deg, #18107b 0%, #4a3bc8 100%);
    box-shadow:
        0 12px 40px rgba(24, 16, 123, 0.5),
        0 8px 20px rgba(74, 59, 200, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.show-card.upcoming::before {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.15) 100%);
    opacity: 0.8;
}

/* Status Badges */
.status-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    font-size: 0.8rem;
    font-weight: 700;
    letter-spacing: 1px;
}

.status-badge.live {
    background: rgba(165, 32, 119, 0.2);
    color: rgba(165, 32, 119, 1);
    border: 1px solid rgba(165, 32, 119, 1);
}

.status-badge.upcoming {
    background: rgba(24, 16, 123, 0.2);
    color: rgba(24, 16, 123, 1);
    border: 1px solid rgba(24, 16, 123, 1);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: currentColor;
    animation: livePulse 1s ease-in-out infinite;
}

.status-icon {
    font-size: 0.7rem;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(3px); }
}

/* Show Content */
.show-content {
    position: relative;
    z-index: 5;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 60px;
}

.show-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 18px;
}

.time-display {
    font-size: 1.35rem;
    font-weight: 700;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}

.day-display {
    font-size: 0.8rem;
    font-weight: 600;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 1.2px;
    padding: 5px 14px;
    background: linear-gradient(135deg, #18107b 0%, #a52077 100%);
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(165, 32, 119, 0.2);
}

.show-title {
    font-size: 1.6rem;
    font-weight: 800;
    color: #ffffff;
    margin: 0 0 8px 0;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.show-host {
    font-size: 1rem;
    color: rgba(24, 16, 123, 1);
    margin: 0 0 12px 0;
    font-weight: 500;
}

.show-description {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
    margin: 0 0 16px 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.show-status-badge {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    padding: 0 28px 20px 28px;
}

.live-now-badge {
    background: linear-gradient(135deg, #ff0040 0%, #ff3366 100%);
    color: #ffffff;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    padding: 8px 20px;
    border-radius: 25px;
    box-shadow:
        0 4px 15px rgba(255, 0, 64, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    animation: liveBadgePulse 2s ease-in-out infinite;
}

.today-badge {
    background: linear-gradient(135deg, #18107b 0%, #4a3bc8 100%);
    color: #ffffff;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding: 6px 16px;
    border-radius: 20px;
    box-shadow:
        0 3px 10px rgba(24, 16, 123, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.show-type-icon {
    position: absolute;
    bottom: 15px;
    right: 15px;
    font-size: 2rem;
    opacity: 0.4;
    transition: all 0.3s ease;
}

.show-card:hover .show-type-icon {
    opacity: 1;
    transform: scale(1.2);
}

/* Card Glow Effect */
.card-glow-effect {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 0, 64, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.show-card:hover .card-glow-effect {
    opacity: 1;
}

/* Schedule Navigation */
.schedule-navigation {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 40px;
    align-items: center;
    padding: 40px;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(10px);
}

.schedule-nav-buttons {
    display: flex;
    gap: 15px;
}

.nav-btn {
    padding: 12px 25px;
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-btn:hover,
.nav-btn.active {
    border-color: #ff0040;
    color: #ff0040;
    background: rgba(255, 0, 64, 0.1);
    transform: translateY(-2px);
}

.schedule-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.action-btn.primary {
    background: linear-gradient(135deg, #ff0040, #ff3366);
    color: #ffffff;
    border: none;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #ff3366, #ff0040);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 0, 64, 0.3);
}

.action-btn.secondary {
    background: transparent;
    color: #00ffff;
    border: 2px solid #00ffff;
}

.action-btn.secondary:hover {
    background: rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1.2rem;
}

/* Floating Elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.floating-note,
.floating-cross {
    position: absolute;
    font-size: 1.5rem;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.floating-note {
    color: #ffff00;
}

.floating-cross {
    color: #ffffff;
}

.note-1 {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.note-2 {
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.note-3 {
    bottom: 30%;
    left: 5%;
    animation-delay: 4s;
}

.cross-1 {
    top: 80%;
    right: 5%;
    animation-delay: 1s;
}

.cross-2 {
    top: 40%;
    left: 85%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
        opacity: 0.3;
    }
}

/* No Shows Message */
.no-shows-message {
    text-align: center;
    padding: 60px 20px;
    color: #ffffff;
    opacity: 0.7;
}

.no-shows-message p {
    font-size: 1.2rem;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .broadcast-schedule-section {
        padding: 60px 0;
    }
    
    .schedule-main-title {
        font-size: 3rem;
    }
    
    .schedule-title-wrapper {
        flex-direction: column;
        gap: 20px;
    }
    
    .now-playing-card {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 30px 20px;
        text-align: center;
    }
    
    .current-show-title {
        font-size: 2.2rem;
    }
    
    .shows-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .carousel-navigation {
        flex-direction: column;
        gap: 20px;
    }
    
    .day-indicators {
        order: -1;
    }
    
    .schedule-navigation {
        grid-template-columns: 1fr;
        gap: 25px;
        padding: 25px 20px;
        text-align: center;
    }
    
    .schedule-nav-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .schedule-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .schedule-main-title {
        font-size: 2.5rem;
    }
    
    .upcoming-title {
        font-size: 2rem;
    }
    
    .show-card {
        padding: 20px;
    }
    
    .now-playing-card {
        padding: 25px 15px;
    }
    
    .current-show-title {
        font-size: 1.8rem;
    }
    
    .current-show-time {
        font-size: 1.3rem;
    }
    
    .nav-btn {
        padding: 10px 18px;
        font-size: 0.9rem;
    }
    
    .action-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}

/* ===================================================================
   TESTIMONIALS WITHIN BROADCAST SCHEDULE - INTEGRATED STYLING
   =================================================================== */

.testimonials-within-broadcast {
    margin-top: 80px;
    position: relative;
    z-index: 3;
}

.testimonial-broadcast-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 40px;
    width: 100%;
    margin: 0 auto;
}

/* ===================================================================
   TESTIMONIAL DISPLAY - INTEGRATED WITH BROADCAST ANIMATIONS
   =================================================================== */

.testimonial-display-broadcast {
    width: 100%;
    position: relative;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    padding: 30px 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.testimonial-container-broadcast {
    position: relative;
    width: 100%;
    height: 100%;
}

.testimonial-item-broadcast {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%) translateY(20px) scale(0.95);
    opacity: 0;
    visibility: hidden;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.testimonial-item-broadcast.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(-50%) translateY(0) scale(1);
}

/* Testimonial Content - Matching Broadcast Styling */
.testimonial-content-broadcast {
    text-align: center;
    padding: 0 15px;
    position: relative;
    z-index: 5;
    width: 100%;
}

/* Quote Icon - Integrated with Broadcast Theme */
.quote-icon-broadcast {
    color: rgba(165, 32, 119, 0.3);
    margin-bottom: 15px;
    transform: rotate(180deg);
    animation: quotePulse 4s ease-in-out infinite;
}

@keyframes quotePulse {
    0%, 100% { 
        opacity: 0.3; 
        transform: rotate(180deg) scale(1);
    }
    50% { 
        opacity: 0.6; 
        transform: rotate(180deg) scale(1.1);
    }
}

.quote-icon-broadcast svg {
    width: 48px;
    height: 38px;
}

/* Testimonial Text - Matching Broadcast Typography */
.testimonial-text-broadcast {
    font-size: 1.3rem;
    line-height: 1.5;
    color: #333333;
    font-weight: 500;
    margin: 0 0 20px 0;
    font-style: italic;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}

.testimonial-text-broadcast::before,
.testimonial-text-broadcast::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 2px;
    background: linear-gradient(90deg, rgba(165, 32, 119, 1), rgba(24, 16, 123, 1));
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.testimonial-text-broadcast::after {
    top: auto;
    bottom: -10px;
}

/* Author Info - Integrated Styling */
.testimonial-author-broadcast {
    display: flex;
    justify-content: center;
    align-items: center;
}

.author-info-broadcast {
    text-align: center;
    position: relative;
}

.author-name-broadcast {
    font-size: 1.2rem;
    font-weight: 700;
    color: rgba(24, 16, 123, 1);
    margin: 0 0 5px 0;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.author-location-broadcast {
    font-size: 1rem;
    color: rgba(165, 32, 119, 0.8);
    margin: 0;
    opacity: 0.9;
    font-weight: 500;
}

/* ===================================================================
   PROGRESS INDICATORS - MATCHING BROADCAST DESIGN
   =================================================================== */

.testimonial-indicators-broadcast {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.indicator-broadcast {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid rgba(24, 16, 123, 0.3);
    background: transparent;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0;
    position: relative;
    overflow: hidden;
}

.indicator-broadcast.active {
    background: linear-gradient(135deg, rgba(165, 32, 119, 1), rgba(24, 16, 123, 1));
    border-color: rgba(165, 32, 119, 1);
    transform: scale(1.3);
    box-shadow: 0 0 15px rgba(165, 32, 119, 0.4);
}

.indicator-broadcast:hover {
    border-color: rgba(165, 32, 119, 0.7);
    transform: scale(1.1);
    background: rgba(165, 32, 119, 0.2);
}

.indicator-broadcast:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(165, 32, 119, 0.3);
}

/* ===================================================================
   CTA BUTTON - BROADCAST THEME INTEGRATION
   =================================================================== */

.testimonials-cta-broadcast {
    margin-top: 30px;
}

.view-testimonials-btn-broadcast {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 15px 30px;
    background: transparent;
    color: rgba(24, 16, 123, 1);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 700;
    border: 2px solid rgba(24, 16, 123, 0.3);
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.view-testimonials-btn-broadcast::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(165, 32, 119, 0.1), transparent);
    transition: left 0.6s ease;
}

.view-testimonials-btn-broadcast:hover::before {
    left: 100%;
}

.view-testimonials-btn-broadcast:hover {
    color: #ffffff;
    background: linear-gradient(135deg, rgba(165, 32, 119, 1), rgba(24, 16, 123, 1));
    border-color: rgba(165, 32, 119, 1);
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(165, 32, 119, 0.3);
}

.view-testimonials-btn-broadcast svg {
    transition: transform 0.3s ease;
}

.view-testimonials-btn-broadcast:hover svg {
    transform: translateX(5px);
}

.view-testimonials-btn-broadcast:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(165, 32, 119, 0.4);
}

/* ===================================================================
   INTEGRATED ANIMATIONS - MATCHING BROADCAST WAVES
   =================================================================== */

/* Testimonial entrance animation */
@keyframes testimonialSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50%) translateY(40px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) translateY(0) scale(1);
    }
}

.testimonial-item-broadcast.active {
    animation: testimonialSlideIn 1s ease-out;
}

/* ===================================================================
   RESPONSIVE DESIGN - MOBILE OPTIMIZED
   =================================================================== */

@media (max-width: 768px) {
    .testimonials-within-broadcast {
        margin-top: 50px;
    }
    
    .testimonial-broadcast-wrapper {
        gap: 25px;
        padding: 0 15px;
    }
    
    .testimonial-display-broadcast {
        min-height: 180px;
        padding: 25px 15px;
    }
    
    .testimonial-text-broadcast {
        font-size: 1.1rem;
        line-height: 1.4;
        margin-bottom: 15px;
    }
    
    .quote-icon-broadcast svg {
        width: 36px;
        height: 28px;
    }
    
    .author-name-broadcast {
        font-size: 1.1rem;
    }
    
    .author-location-broadcast {
        font-size: 0.95rem;
    }
    
    .testimonial-indicators-broadcast {
        gap: 12px;
    }
    
    .indicator-broadcast {
        width: 10px;
        height: 10px;
    }
    
    .view-testimonials-btn-broadcast {
        padding: 12px 24px;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .testimonial-display-broadcast {
        min-height: 160px;
        padding: 20px 10px;
    }
    
    .testimonial-content-broadcast {
        padding: 0 5px;
    }
    
    .testimonial-text-broadcast {
        font-size: 1rem;
    }
    
    .author-name-broadcast {
        font-size: 1rem;
    }
    
    .author-location-broadcast {
        font-size: 0.95rem;
    }
    
    .testimonial-indicators-broadcast {
        gap: 10px;
    }
    
    .indicator-broadcast {
        width: 8px;
        height: 8px;
    }
}

/* ===================================================================
   ACCESSIBILITY & REDUCED MOTION
   =================================================================== */

@media (prefers-reduced-motion: reduce) {
    .testimonial-item-broadcast {
        transition: opacity 0.3s ease;
        transform: translateY(-50%);
    }
    
    .testimonial-item-broadcast.active {
        animation: none;
        transform: translateY(-50%);
    }
    
    .view-testimonials-btn-broadcast:hover {
        transform: none;
    }
    
    .indicator-broadcast:hover,
    .indicator-broadcast.active {
        transform: none;
    }
    
    .quote-icon-broadcast {
        animation: none;
    }
}