<?php
/**
 * Template for displaying single renungan posts
 */

get_header();
?>

<div class="efrai-container">
    <div class="efrai-main-content">
        <?php while (have_posts()) : the_post(); ?>

            <article id="post-<?php the_ID(); ?>" <?php post_class('efrai-renungan-single'); ?>>
                <header class="efrai-renungan-header">
                    <h1 class="efrai-renungan-title"><?php the_title(); ?></h1>

                    <div class="efrai-renungan-meta">
                        <?php if ($tanggal = get_post_meta(get_the_ID(), '_efrai_tanggal', true)) : ?>
                            <span class="efrai-meta-item efrai-date">
                                <span class="dashicons dashicons-calendar-alt"></span>
                                <?php echo esc_html($tanggal); ?>
                            </span>
                        <?php endif; ?>

                        <?php if ($ayat = get_post_meta(get_the_ID(), '_efrai_ayat', true)) : ?>
                            <span class="efrai-meta-item efrai-verse">
                                <span class="dashicons dashicons-book-alt"></span>
                                <?php echo esc_html($ayat); ?>
                            </span>
                        <?php endif; ?>

                        <?php if ($tema = get_post_meta(get_the_ID(), '_efrai_tema', true)) : ?>
                            <span class="efrai-meta-item efrai-theme">
                                <span class="dashicons dashicons-tag"></span>
                                <?php echo esc_html($tema); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </header>

                <?php if (has_post_thumbnail()) : ?>
                    <div class="efrai-renungan-featured-image">
                        <?php the_post_thumbnail('large', array('class' => 'efrai-featured-image')); ?>
                    </div>
                <?php endif; ?>

                <div class="efrai-renungan-content">
                    <?php the_content(); ?>
                </div>

                <?php if ($generated_image = get_post_meta(get_the_ID(), '_efrai_generated_image', true)) : ?>
                    <div class="efrai-generated-image-container">
                        <h3><?php echo esc_html__('AI Generated Image', 'elshadaifm-renungan-ai'); ?></h3>
                        <img src="<?php echo esc_url($generated_image); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" class="efrai-generated-image">
                    </div>
                <?php endif; ?>

                <footer class="efrai-renungan-footer">
                    <div class="efrai-sharing">
                        <h4><?php echo esc_html__('Bagikan Renungan Ini', 'elshadaifm-renungan-ai'); ?></h4>
                        <?php echo do_shortcode('[addtoany]'); ?>
                    </div>

                    <div class="efrai-navigation">
                        <?php
                        $prev_post = get_previous_post();
                        $next_post = get_next_post();
                        ?>

                        <?php if ($prev_post) : ?>
                            <div class="efrai-nav-prev">
                                <a href="<?php echo get_permalink($prev_post->ID); ?>" class="efrai-nav-link">
                                    <span class="dashicons dashicons-arrow-left-alt2"></span>
                                    <span class="efrai-nav-text"><?php echo esc_html__('Renungan Sebelumnya', 'elshadaifm-renungan-ai'); ?></span>
                                </a>
                            </div>
                        <?php endif; ?>

                        <?php if ($next_post) : ?>
                            <div class="efrai-nav-next">
                                <a href="<?php echo get_permalink($next_post->ID); ?>" class="efrai-nav-link">
                                    <span class="efrai-nav-text"><?php echo esc_html__('Renungan Berikutnya', 'elshadaifm-renungan-ai'); ?></span>
                                    <span class="dashicons dashicons-arrow-right-alt2"></span>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </footer>
            </article>

        <?php endwhile; ?>
    </div>

    <?php get_sidebar(); ?>
</div>

<?php get_footer(); ?>