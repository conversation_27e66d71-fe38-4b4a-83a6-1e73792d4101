<?php
/**
 * Bulk Actions for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class EfraI_Bulk_Actions {

    /**
     * Add custom bulk actions
     */
    public function add_bulk_actions($bulk_actions) {
        $bulk_actions['generate_ai_image'] = __('Generate AI Image', 'elshadaifm-renungan-ai');
        return $bulk_actions;
    }

    /**
     * Handle bulk actions
     */
    public function handle_bulk_actions($redirect_to, $doaction, $post_ids) {
        if ($doaction !== 'generate_ai_image') {
            return $redirect_to;
        }

        $processed = 0;
        $errors = 0;

        foreach ($post_ids as $post_id) {
            $result = $this->generate_image_for_post($post_id);
            if ($result) {
                $processed++;
            } else {
                $errors++;
            }
        }

        $redirect_to = add_query_arg(array(
            'bulk_action_processed' => true,
            'processed' => $processed,
            'errors' => $errors,
            'post_type' => 'renungan'
        ), $redirect_to);

        return $redirect_to;
    }

    /**
     * Generate AI image for a specific post
     */
    private function generate_image_for_post($post_id) {
        global $efrai_plugin;

        if (!current_user_can('edit_post', $post_id)) {
            return false;
        }

        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'renungan') {
            return false;
        }

        // Check if already has AI image
        if ($efrai_plugin->get_utils()->has_ai_image($post_id)) {
            return false;
        }

        // Generate image
        $image_data = $efrai_plugin->google_ai->generate_image($post->post_title);
        if (!$image_data) {
            return false;
        }

        try {
            // Attach image to post
            $attach_id = $efrai_plugin->get_utils()->attach_base64_image_to_post($image_data, $post_id);
            if ($attach_id) {
                // Set as featured image
                set_post_thumbnail($post_id, $attach_id);
                // Mark as AI generated
                update_post_meta($post_id, '_efrai_generated_image', true);
                return true;
            }
        } catch (Exception $e) {
            error_log('EfraI: Failed to attach AI image: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Add admin notice for bulk actions
     */
    public function add_admin_notice() {
        if (!isset($_GET['bulk_action_processed']) || $_GET['bulk_action_processed'] !== 'true') {
            return;
        }

        $processed = isset($_GET['processed']) ? intval($_GET['processed']) : 0;
        $errors = isset($_GET['errors']) ? intval($_GET['errors']) : 0;
        $post_type = isset($_GET['post_type']) ? sanitize_text_field($_GET['post_type']) : '';

        if ($post_type !== 'renungan') {
            return;
        }

        if ($processed > 0) {
            echo '<div id="message" class="notice notice-success is-dismissible">';
            echo '<p>' . sprintf(
                esc_html__('%d AI images generated successfully.', 'elshadaifm-renungan-ai'),
                $processed
            ) . '</p>';
            echo '</div>';
        }

        if ($errors > 0) {
            echo '<div id="message" class="notice notice-error is-dismissible">';
            echo '<p>' . sprintf(
                esc_html__('%d errors occurred while generating AI images.', 'elshadaifm-renungan-ai'),
                $errors
            ) . '</p>';
            echo '</div>';
        }
    }

    /**
     * Add custom columns to renungan list
     */
    public function add_custom_columns($columns) {
        $columns['tanggal'] = __('Tanggal', 'elshadaifm-renungan-ai');
        $columns['ayat'] = __('Ayat', 'elshadaifm-renungan-ai');
        $columns['ai_image'] = __('AI Image', 'elshadaifm-renungan-ai');
        return $columns;
    }

    /**
     * Display custom column content
     */
    public function display_custom_columns($column, $post_id) {
        global $efrai_plugin;

        switch ($column) {
            case 'tanggal':
                $tanggal = get_post_meta($post_id, '_efrai_tanggal', true);
                echo $tanggal ? esc_html($tanggal) : '-';
                break;

            case 'ayat':
                $ayat = get_post_meta($post_id, '_efrai_ayat', true);
                echo $ayat ? esc_html($ayat) : '-';
                break;

            case 'ai_image':
                if ($efrai_plugin->get_utils()->has_ai_image($post_id)) {
                    echo '<span style="color: #28a745;">✓ ' . esc_html__('Generated', 'elshadaifm-renungan-ai') . '</span>';
                } else {
                    echo '<span style="color: #dc3545;">✗ ' . esc_html__('No', 'elshadaifm-renungan-ai') . '</span>';
                }
                break;
        }
    }

    /**
     * Make custom columns sortable
     */
    public function make_custom_columns_sortable($columns) {
        $columns['tanggal'] = 'tanggal';
        return $columns;
    }

    /**
     * Add quick edit fields
     */
    public function add_quick_edit_fields($column_name, $post_type) {
        if ($post_type !== 'renungan') {
            return;
        }

        switch ($column_name) {
            case 'tanggal':
                ?>
                <fieldset class="inline-edit-col-right">
                    <div class="inline-edit-col">
                        <label>
                            <span class="title"><?php echo esc_html__('Tanggal', 'elshadaifm-renungan-ai'); ?></span>
                            <span class="input-text-wrap">
                                <input type="date" name="efrai_tanggal" class="efrai-quick-edit-tanggal">
                            </span>
                        </label>
                    </div>
                </fieldset>
                <?php
                break;

            case 'ayat':
                ?>
                <fieldset class="inline-edit-col-right">
                    <div class="inline-edit-col">
                        <label>
                            <span class="title"><?php echo esc_html__('Ayat', 'elshadaifm-renungan-ai'); ?></span>
                            <span class="input-text-wrap">
                                <input type="text" name="efrai_ayat" class="efrai-quick-edit-ayat">
                            </span>
                        </label>
                    </div>
                </fieldset>
                <?php
                break;
        }
    }

    /**
     * Save quick edit data
     */
    public function save_quick_edit_data($post_id, $post) {
        if ($post->post_type !== 'renungan') {
            return;
        }

        if (isset($_POST['efrai_tanggal'])) {
            update_post_meta($post_id, '_efrai_tanggal', sanitize_text_field($_POST['efrai_tanggal']));
        }

        if (isset($_POST['efrai_ayat'])) {
            update_post_meta($post_id, '_efrai_ayat', sanitize_text_field($_POST['efrai_ayat']));
        }
    }
}