<?php
/**
 * Plugin Name: Elshadaifm Broadcast Schedule
 * Plugin URI: https://elshadaifm.local
 * Description: Plugin untuk mengelola jadwal siaran dan profile penyiar radio Elshadaifm
 * Version: 1.0.0
 * Author: Claude Code
 * Text Domain: elshadaifm-broadcast
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('EFBS_VERSION', '1.0.0');
define('EFBS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('EFBS_PLUGIN_PATH', plugin_dir_path(__FILE__));

// Main plugin class
class ElshadaifmBroadcastSchedule {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Load text domain
        load_plugin_textdomain('elshadaifm-broadcast', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize custom post types
        $this->register_post_types();
        
        // Add admin menus
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Add shortcodes
        add_shortcode('broadcast_schedule', array($this, 'schedule_shortcode'));
        add_shortcode('broadcaster_profile', array($this, 'broadcaster_shortcode'));
        
        // Load meta boxes
        require_once EFBS_PLUGIN_PATH . 'admin/meta-boxes.php';
        
        // Admin-only AJAX handlers
        add_action('wp_ajax_get_calendar_events', array($this, 'get_calendar_events'));
        add_action('wp_ajax_update_event_time', array($this, 'update_event_time'));
        add_action('wp_ajax_create_event', array($this, 'create_event'));
        add_action('wp_ajax_delete_event', array($this, 'delete_event'));
        add_action('wp_ajax_get_broadcasters', array($this, 'get_broadcasters'));
    }
    
    public function register_post_types() {
        // Register Broadcaster post type
        register_post_type('broadcaster', array(
            'labels' => array(
                'name' => __('Penyiar', 'elshadaifm-broadcast'),
                'singular_name' => __('Penyiar', 'elshadaifm-broadcast'),
                'add_new' => __('Tambah Penyiar', 'elshadaifm-broadcast'),
                'add_new_item' => __('Tambah Penyiar Baru', 'elshadaifm-broadcast'),
                'edit_item' => __('Edit Penyiar', 'elshadaifm-broadcast'),
                'view_item' => __('Lihat Penyiar', 'elshadaifm-broadcast'),
                'search_items' => __('Cari Penyiar', 'elshadaifm-broadcast'),
            ),
            'public' => true,
            'show_ui' => true,
            'show_in_menu' => false,
            'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
            'capability_type' => 'post',
            'rewrite' => array('slug' => 'penyiar'),
        ));
        
        // Register Broadcast Schedule post type
        register_post_type('broadcast_schedule', array(
            'labels' => array(
                'name' => __('Jadwal Siaran', 'elshadaifm-broadcast'),
                'singular_name' => __('Jadwal Siaran', 'elshadaifm-broadcast'),
                'add_new' => __('Tambah Jadwal', 'elshadaifm-broadcast'),
                'add_new_item' => __('Tambah Jadwal Baru', 'elshadaifm-broadcast'),
                'edit_item' => __('Edit Jadwal', 'elshadaifm-broadcast'),
                'view_item' => __('Lihat Jadwal', 'elshadaifm-broadcast'),
                'search_items' => __('Cari Jadwal', 'elshadaifm-broadcast'),
            ),
            'public' => true,
            'show_ui' => true,
            'show_in_menu' => false,
            'supports' => array('title', 'editor', 'custom-fields'),
            'capability_type' => 'post',
            'rewrite' => array('slug' => 'jadwal-siaran'),
        ));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            __('Jadwal Siaran', 'elshadaifm-broadcast'),
            __('Jadwal Siaran', 'elshadaifm-broadcast'),
            'manage_options',
            'broadcast-schedule',
            array($this, 'admin_page'),
            'dashicons-calendar-alt',
            6
        );
        
        add_submenu_page(
            'broadcast-schedule',
            __('Semua Jadwal', 'elshadaifm-broadcast'),
            __('Semua Jadwal', 'elshadaifm-broadcast'),
            'manage_options',
            'edit.php?post_type=broadcast_schedule'
        );
        
        add_submenu_page(
            'broadcast-schedule',
            __('Tambah Jadwal', 'elshadaifm-broadcast'),
            __('Tambah Jadwal', 'elshadaifm-broadcast'),
            'manage_options',
            'post-new.php?post_type=broadcast_schedule'
        );
        
        add_submenu_page(
            'broadcast-schedule',
            __('Penyiar', 'elshadaifm-broadcast'),
            __('Penyiar', 'elshadaifm-broadcast'),
            'manage_options',
            'edit.php?post_type=broadcaster'
        );
        
        add_submenu_page(
            'broadcast-schedule',
            __('Tambah Penyiar', 'elshadaifm-broadcast'),
            __('Tambah Penyiar', 'elshadaifm-broadcast'),
            'manage_options',
            'post-new.php?post_type=broadcaster'
        );
        
        add_submenu_page(
            'broadcast-schedule',
            __('Calendar Manager', 'elshadaifm-broadcast'),
            __('Calendar Manager', 'elshadaifm-broadcast'),
            'manage_options',
            'broadcast-calendar',
            array($this, 'admin_calendar_page')
        );
    }
    
    public function admin_page() {
        include EFBS_PLUGIN_PATH . 'admin/admin-page.php';
    }
    
    public function admin_calendar_page() {
        include EFBS_PLUGIN_PATH . 'admin/admin-calendar.php';
    }
    
    public function enqueue_frontend_scripts() {
        wp_enqueue_style('efbs-frontend', EFBS_PLUGIN_URL . 'assets/css/frontend.css', array(), EFBS_VERSION);
        wp_enqueue_script('efbs-frontend', EFBS_PLUGIN_URL . 'assets/js/frontend.js', array('jquery'), EFBS_VERSION, true);
    }
    
    public function enqueue_admin_scripts($hook) {
        // Enqueue media scripts for broadcaster post type
        if ($hook == 'post.php' || $hook == 'post-new.php') {
            global $post_type;
            if ($post_type == 'broadcaster') {
                wp_enqueue_media();
            }
        }
        
        if (strpos($hook, 'broadcast-schedule') !== false || 
            strpos($hook, 'broadcast_schedule') !== false || 
            strpos($hook, 'broadcaster') !== false ||
            strpos($hook, 'broadcast-calendar') !== false) {
            
            wp_enqueue_style('efbs-admin', EFBS_PLUGIN_URL . 'assets/css/admin.css', array(), EFBS_VERSION);
            
            // Load FullCalendar for calendar page
            if (strpos($hook, 'broadcast-calendar') !== false) {
                wp_enqueue_style('fullcalendar', EFBS_PLUGIN_URL . 'assets/css/fullcalendar.css', array(), EFBS_VERSION);
                wp_enqueue_script('fullcalendar', EFBS_PLUGIN_URL . 'assets/js/fullcalendar.min.js', array(), EFBS_VERSION, true);
                wp_enqueue_script('efbs-admin', EFBS_PLUGIN_URL . 'assets/js/admin.js', array('jquery', 'fullcalendar'), EFBS_VERSION, true);
            } else {
                wp_enqueue_script('efbs-admin', EFBS_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), EFBS_VERSION, true);
            }
            
            wp_localize_script('efbs-admin', 'efbs_admin_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('efbs_admin_nonce')
            ));
        }
    }
    
    public function schedule_shortcode($atts) {
        $atts = shortcode_atts(array(
            'type' => 'all', // all, regular, special
            'limit' => 10,
            'show_date' => 'yes',
            'show_broadcaster' => 'yes'
        ), $atts);
        
        ob_start();
        include EFBS_PLUGIN_PATH . 'templates/schedule-shortcode.php';
        return ob_get_clean();
    }
    
    public function broadcaster_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'show_schedule' => 'yes',
            'show_bio' => 'yes'
        ), $atts);
        
        ob_start();
        include EFBS_PLUGIN_PATH . 'templates/broadcaster-shortcode.php';
        return ob_get_clean();
    }
    
    public function get_calendar_events() {
        // Admin-only access
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        // Admin nonce validation only
        if (!wp_verify_nonce($_POST['nonce'], 'efbs_admin_nonce')) {
            wp_send_json_error('Invalid nonce');
        }
        
        $start = sanitize_text_field($_POST['start']);
        $end = sanitize_text_field($_POST['end']);
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'efbs_schedules';
        
        $events = array();
        
        // Get regular schedules and convert to events for date range
        $regular_schedules = $wpdb->get_results("
            SELECT s.*, p.post_title as program_title, p.post_content as program_description,
                   b.post_title as broadcaster_name, b.ID as broadcaster_id
            FROM $table_name s
            LEFT JOIN {$wpdb->posts} p ON s.post_id = p.ID
            LEFT JOIN {$wpdb->posts} b ON s.broadcaster_id = b.ID
            WHERE s.status = 'active' 
            AND s.is_regular = 1
            AND p.post_status = 'publish'
        ");
        
        // Convert regular schedules to recurring events
        foreach ($regular_schedules as $schedule) {
            $current_date = new DateTime($start);
            $end_date = new DateTime($end);
            
            while ($current_date <= $end_date) {
                if ($current_date->format('w') == $schedule->day_of_week) {
                    $event_date = $current_date->format('Y-m-d');
                    
                    $events[] = array(
                        'id' => 'regular_' . $schedule->id . '_' . $event_date,
                        'title' => $schedule->program_title,
                        'start' => $event_date . 'T' . $schedule->start_time,
                        'end' => $event_date . 'T' . $schedule->end_time,
                        'backgroundColor' => '#3788d8',
                        'borderColor' => '#3788d8',
                        'extendedProps' => array(
                            'broadcaster' => $schedule->broadcaster_name,
                            'description' => $schedule->program_description,
                            'type' => 'regular'
                        )
                    );
                }
                $current_date->modify('+1 day');
            }
        }
        
        // Get special events
        $special_schedules = $wpdb->get_results($wpdb->prepare("
            SELECT s.*, p.post_title as program_title, p.post_content as program_description,
                   b.post_title as broadcaster_name, b.ID as broadcaster_id
            FROM $table_name s
            LEFT JOIN {$wpdb->posts} p ON s.post_id = p.ID
            LEFT JOIN {$wpdb->posts} b ON s.broadcaster_id = b.ID
            WHERE s.status = 'active' 
            AND s.is_regular = 0
            AND s.specific_date BETWEEN %s AND %s
            AND p.post_status = 'publish'
        ", $start, $end));
        
        foreach ($special_schedules as $schedule) {
            $events[] = array(
                'id' => 'special_' . $schedule->id,
                'title' => $schedule->program_title,
                'start' => $schedule->specific_date . 'T' . $schedule->start_time,
                'end' => $schedule->specific_date . 'T' . $schedule->end_time,
                'backgroundColor' => '#ff6b6b',
                'borderColor' => '#ff6b6b',
                'extendedProps' => array(
                    'broadcaster' => $schedule->broadcaster_name,
                    'description' => $schedule->program_description,
                    'type' => 'special'
                )
            );
        }
        
        wp_send_json_success($events);
    }
    
    public function update_event_time() {
        check_ajax_referer('efbs_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $event_id = sanitize_text_field($_POST['event_id']);
        $start = sanitize_text_field($_POST['start']);
        $end = sanitize_text_field($_POST['end']);
        
        // Extract schedule ID and date from event ID
        if (strpos($event_id, 'regular_') === 0) {
            $parts = explode('_', $event_id);
            $schedule_id = intval($parts[1]);
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'efbs_schedules';
            
            $start_time = date('H:i:s', strtotime($start));
            $end_time = date('H:i:s', strtotime($end));
            
            $result = $wpdb->update(
                $table_name,
                array(
                    'start_time' => $start_time,
                    'end_time' => $end_time
                ),
                array('id' => $schedule_id),
                array('%s', '%s'),
                array('%d')
            );
            
            if ($result !== false) {
                wp_send_json_success('Event updated successfully');
            } else {
                wp_send_json_error('Failed to update event');
            }
        } elseif (strpos($event_id, 'special_') === 0) {
            $schedule_id = intval(str_replace('special_', '', $event_id));
            
            global $wpdb;
            $table_name = $wpdb->prefix . 'efbs_schedules';
            
            $specific_date = date('Y-m-d', strtotime($start));
            $start_time = date('H:i:s', strtotime($start));
            $end_time = date('H:i:s', strtotime($end));
            
            $result = $wpdb->update(
                $table_name,
                array(
                    'specific_date' => $specific_date,
                    'start_time' => $start_time,
                    'end_time' => $end_time
                ),
                array('id' => $schedule_id),
                array('%s', '%s', '%s'),
                array('%d')
            );
            
            if ($result !== false) {
                wp_send_json_success('Event updated successfully');
            } else {
                wp_send_json_error('Failed to update event');
            }
        }
        
        wp_send_json_error('Invalid event ID');
    }
    
    public function create_event() {
        check_ajax_referer('efbs_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $title = sanitize_text_field($_POST['title']);
        $broadcaster_id = intval($_POST['broadcaster_id']);
        $schedule_type = sanitize_text_field($_POST['schedule_type']);
        $start_time = sanitize_text_field($_POST['start_time']);
        $end_time = sanitize_text_field($_POST['end_time']);
        $description = sanitize_textarea_field($_POST['description']);
        
        // Create program post
        $program_post = array(
            'post_title' => $title,
            'post_content' => $description,
            'post_status' => 'publish',
            'post_type' => 'broadcast_schedule'
        );
        
        $post_id = wp_insert_post($program_post);
        
        if (is_wp_error($post_id)) {
            wp_send_json_error('Failed to create program post');
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'efbs_schedules';
        
        $schedule_data = array(
            'post_id' => $post_id,
            'broadcaster_id' => $broadcaster_id,
            'start_time' => $start_time . ':00',
            'end_time' => $end_time . ':00',
            'status' => 'active'
        );
        
        if ($schedule_type === 'regular') {
            $schedule_data['day_of_week'] = intval($_POST['day_of_week']);
            $schedule_data['is_regular'] = 1;
        } else {
            $schedule_data['specific_date'] = sanitize_text_field($_POST['specific_date']);
            $schedule_data['is_regular'] = 0;
            $schedule_data['day_of_week'] = 0; // Default value
        }
        
        $result = $wpdb->insert($table_name, $schedule_data);
        
        if ($result) {
            wp_send_json_success('Event created successfully');
        } else {
            wp_delete_post($post_id, true);
            wp_send_json_error('Failed to create schedule');
        }
    }
    
    public function delete_event() {
        check_ajax_referer('efbs_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }
        
        $event_id = sanitize_text_field($_POST['event_id']);
        
        // Extract schedule ID from event ID
        if (strpos($event_id, 'regular_') === 0) {
            $parts = explode('_', $event_id);
            $schedule_id = intval($parts[1]);
        } elseif (strpos($event_id, 'special_') === 0) {
            $schedule_id = intval(str_replace('special_', '', $event_id));
        } else {
            wp_send_json_error('Invalid event ID');
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'efbs_schedules';
        
        // Get post_id before deleting
        $post_id = $wpdb->get_var($wpdb->prepare("SELECT post_id FROM $table_name WHERE id = %d", $schedule_id));
        
        // Delete schedule
        $result = $wpdb->delete($table_name, array('id' => $schedule_id), array('%d'));
        
        if ($result) {
            // Delete associated post
            if ($post_id) {
                wp_delete_post($post_id, true);
            }
            wp_send_json_success('Event deleted successfully');
        } else {
            wp_send_json_error('Failed to delete event');
        }
    }
    
    public function get_broadcasters() {
        check_ajax_referer('efbs_admin_nonce', 'nonce');
        
        $broadcasters = get_posts(array(
            'post_type' => 'broadcaster',
            'post_status' => 'publish',
            'numberposts' => -1,
            'orderby' => 'title',
            'order' => 'ASC'
        ));
        
        wp_send_json_success($broadcasters);
    }
    
    // Helper function to get broadcaster profile picture
    public static function get_broadcaster_profile_image($broadcaster_id, $size = 'thumbnail') {
        $profile_image_id = get_post_meta($broadcaster_id, '_broadcaster_profile_image', true);
        
        if ($profile_image_id) {
            return wp_get_attachment_image($profile_image_id, $size);
        }
        
        // Fallback to featured image
        $featured_image_id = get_post_thumbnail_id($broadcaster_id);
        if ($featured_image_id) {
            return wp_get_attachment_image($featured_image_id, $size);
        }
        
        // Default avatar if no image
        return '<div class="broadcaster-avatar-placeholder" style="width: 50px; height: 50px; background: #ddd; display: flex; align-items: center; justify-content: center; border-radius: 50%; color: #666; font-weight: bold;">' . substr(get_the_title($broadcaster_id), 0, 1) . '</div>';
    }
    
    // Helper function to get profile image URL
    public static function get_broadcaster_profile_image_url($broadcaster_id, $size = 'thumbnail') {
        $profile_image_id = get_post_meta($broadcaster_id, '_broadcaster_profile_image', true);
        
        if ($profile_image_id) {
            $image_url = wp_get_attachment_image_url($profile_image_id, $size);
            if ($image_url) {
                return $image_url;
            }
        }
        
        // Fallback to featured image
        $featured_image_id = get_post_thumbnail_id($broadcaster_id);
        if ($featured_image_id) {
            $image_url = wp_get_attachment_image_url($featured_image_id, $size);
            if ($image_url) {
                return $image_url;
            }
        }
        
        return false;
    }
    
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Register post types
        $this->register_post_types();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
      
    }
    
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Create schedules table
        $table_name = $wpdb->prefix . 'efbs_schedules';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            broadcaster_id bigint(20) NOT NULL,
            day_of_week tinyint(1) NOT NULL,
            start_time time NOT NULL,
            end_time time NOT NULL,
            is_regular tinyint(1) DEFAULT 1,
            specific_date date NULL,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY broadcaster_id (broadcaster_id),
            KEY day_of_week (day_of_week),
            KEY is_regular (is_regular)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
}

// Initialize the plugin
new ElshadaifmBroadcastSchedule();