<?php
// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

global $wpdb;
$table_name = $wpdb->prefix . 'efbs_schedules';

// Build query based on attributes
$where_clause = "WHERE s.status = 'active' AND p.post_status = 'publish'";
$params = array();

if ($atts['type'] == 'regular') {
    $where_clause .= " AND s.is_regular = 1";
} elseif ($atts['type'] == 'special') {
    $where_clause .= " AND s.is_regular = 0";
}

$limit_clause = '';
if ($atts['limit'] > 0) {
    $limit_clause = "LIMIT " . intval($atts['limit']);
}

$schedules = $wpdb->get_results("
    SELECT s.*, p.post_title as program_title, p.post_content as program_description,
           b.post_title as broadcaster_name, b.ID as broadcaster_id
    FROM $table_name s
    LEFT JOIN {$wpdb->posts} p ON s.post_id = p.ID
    LEFT JOIN {$wpdb->posts} b ON s.broadcaster_id = b.ID
    $where_clause
    ORDER BY s.day_of_week ASC, s.start_time ASC
    $limit_clause
");

if (!$schedules) {
    echo '<p class="efbs-no-schedules">' . __('Tidak ada jadwal siaran tersedia.', 'elshadaifm-broadcast') . '</p>';
    return;
}

$days = array(
    0 => __('Minggu', 'elshadaifm-broadcast'),
    1 => __('Senin', 'elshadaifm-broadcast'),
    2 => __('Selasa', 'elshadaifm-broadcast'),
    3 => __('Rabu', 'elshadaifm-broadcast'),
    4 => __('Kamis', 'elshadaifm-broadcast'),
    5 => __('Jumat', 'elshadaifm-broadcast'),
    6 => __('Sabtu', 'elshadaifm-broadcast')
);

$current_time = current_time('timestamp');
$current_day = date('w', $current_time);
$current_hour_min = date('H:i', $current_time);
?>

<div class="efbs-schedule-wrapper">
    <?php if ($atts['type'] == 'all' || $atts['type'] == 'regular'): ?>
        <div class="efbs-regular-schedules">
            <h3 class="efbs-section-title"><?php _e('Jadwal Siaran Rutin', 'elshadaifm-broadcast'); ?></h3>
            
            <?php
            $regular_schedules = array_filter($schedules, function($s) { return $s->is_regular == 1; });
            $grouped_by_day = array();
            
            foreach ($regular_schedules as $schedule) {
                $grouped_by_day[$schedule->day_of_week][] = $schedule;
            }
            ?>
            
            <div class="efbs-weekly-schedule">
                <?php foreach ($days as $day_num => $day_name): 
                    $day_class = ($day_num == $current_day) ? 'efbs-current-day' : '';
                ?>
                    <div class="efbs-day-schedule <?php echo $day_class; ?>">
                        <h4 class="efbs-day-name"><?php echo $day_name; ?></h4>
                        
                        <?php if (isset($grouped_by_day[$day_num])): ?>
                            <?php foreach ($grouped_by_day[$day_num] as $schedule): 
                                $is_live = ($day_num == $current_day && 
                                           $current_hour_min >= date('H:i', strtotime($schedule->start_time)) && 
                                           $current_hour_min <= date('H:i', strtotime($schedule->end_time)));
                                $status_class = $is_live ? 'efbs-live' : '';
                            ?>
                                <div class="efbs-schedule-item <?php echo $status_class; ?>">
                                    <div class="efbs-time">
                                        <?php echo date('H:i', strtotime($schedule->start_time)); ?> - 
                                        <?php echo date('H:i', strtotime($schedule->end_time)); ?>
                                        <?php if ($is_live): ?>
                                            <span class="efbs-live-indicator"><?php _e('LIVE', 'elshadaifm-broadcast'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="efbs-program-info">
                                        <h5 class="efbs-program-title"><?php echo esc_html($schedule->program_title); ?></h5>
                                        <?php if ($atts['show_broadcaster'] == 'yes'): ?>
                                            <p class="efbs-broadcaster"><?php echo esc_html($schedule->broadcaster_name); ?></p>
                                        <?php endif; ?>
                                        <?php if ($schedule->program_description): ?>
                                            <p class="efbs-program-desc"><?php echo wp_trim_words($schedule->program_description, 15); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="efbs-no-schedule"><?php _e('Tidak ada siaran', 'elshadaifm-broadcast'); ?></p>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>
    
    <?php if ($atts['type'] == 'all' || $atts['type'] == 'special'): ?>
        <?php
        $special_schedules = array_filter($schedules, function($s) { 
            return $s->is_regular == 0 && strtotime($s->specific_date) >= strtotime('today'); 
        });
        ?>
        
        <?php if ($special_schedules): ?>
            <div class="efbs-special-schedules">
                <h3 class="efbs-section-title"><?php _e('Acara Khusus', 'elshadaifm-broadcast'); ?></h3>
                
                <?php foreach ($special_schedules as $schedule): 
                    $schedule_date = strtotime($schedule->specific_date);
                    $is_today = date('Y-m-d', $schedule_date) == date('Y-m-d', $current_time);
                    $is_live = ($is_today && 
                               $current_hour_min >= date('H:i', strtotime($schedule->start_time)) && 
                               $current_hour_min <= date('H:i', strtotime($schedule->end_time)));
                    $status_class = $is_live ? 'efbs-live' : '';
                ?>
                    <div class="efbs-schedule-item efbs-special-item <?php echo $status_class; ?>">
                        <?php if ($atts['show_date'] == 'yes'): ?>
                            <div class="efbs-special-date">
                                <?php echo date_i18n('l, d F Y', $schedule_date); ?>
                            </div>
                        <?php endif; ?>
                        <div class="efbs-time">
                            <?php echo date('H:i', strtotime($schedule->start_time)); ?> - 
                            <?php echo date('H:i', strtotime($schedule->end_time)); ?>
                            <?php if ($is_live): ?>
                                <span class="efbs-live-indicator"><?php _e('LIVE', 'elshadaifm-broadcast'); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="efbs-program-info">
                            <h5 class="efbs-program-title"><?php echo esc_html($schedule->program_title); ?></h5>
                            <?php if ($atts['show_broadcaster'] == 'yes'): ?>
                                <p class="efbs-broadcaster"><?php echo esc_html($schedule->broadcaster_name); ?></p>
                            <?php endif; ?>
                            <?php if ($schedule->program_description): ?>
                                <p class="efbs-program-desc"><?php echo wp_trim_words($schedule->program_description, 20); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>