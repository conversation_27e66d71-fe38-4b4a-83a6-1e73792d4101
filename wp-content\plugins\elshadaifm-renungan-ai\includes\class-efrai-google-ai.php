<?php
/**
 * Google AI Integration for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class EfraI_Google_AI {

    private $api_key;

    public function __construct($api_key) {
        $this->api_key = $api_key;
    }

    /**
     * Test Google AI connection
     */
    public function test_connection() {
        if (empty($this->api_key)) {
            throw new Exception('Google AI API key not configured');
        }

        $api_url = 'https://generativelanguage.googleapis.com/v1beta/models?key=' . $this->api_key;

        $response = wp_remote_get($api_url, array(
            'timeout' => 10
        ));

        if (is_wp_error($response)) {
            throw new Exception('Connection failed: ' . $response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $error_msg = isset($body['error']['message']) ? $body['error']['message'] : 'Unknown error';
            throw new Exception("API Error ($response_code): $error_msg");
        }

        // Find image generation models
        $image_models = array();
        if (isset($body['models']) && !empty($body['models'])) {
            foreach ($body['models'] as $model) {
                if (strpos($model['name'], 'image') !== false || strpos($model['displayName'], 'Image') !== false) {
                    $image_models[] = $model['name'];
                }
            }
        }

        $result = "✅ Google AI connection successful!\n";
        $result .= "Total models found: " . count($body['models']) . "\n";
        if (!empty($image_models)) {
            $result .= "Image generation models available:\n";
            foreach ($image_models as $model) {
                $result .= "- $model\n";
            }
        } else {
            $result .= "⚠️  No image generation models found. Make sure you have access to Gemini 2.5 Flash Image Preview.";
        }

        return $result;
    }

    /**
     * Generate image from Google AI
     */
    public function generate_image($prompt) {
        if (empty($this->api_key)) {
            throw new Exception('Google AI API key not configured');
        }

        // Using the correct image generation model
        $api_url = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-image-preview:generateContent';

        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'x-goog-api-key' => $this->api_key,
            ),
            'body' => json_encode(array(
                'contents' => array(
                    array(
                        'parts' => array(
                            array(
                                'text' => $prompt
                            )
                        )
                    )
                ),
                'generationConfig' => array(
                    'responseModalities' => array('Image')
                )
            )),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            throw new Exception('Failed to generate image: ' . $response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $error_msg = isset($body['error']['message']) ? $body['error']['message'] : 'Unknown error';
            throw new Exception("API Error ($response_code): $error_msg");
        }

        // Extract image data from response
        if (isset($body['candidates'][0]['content']['parts'])) {
            foreach ($body['candidates'][0]['content']['parts'] as $part) {
                if (isset($part['inline_data']['data'])) {
                    return $part['inline_data']['data'];
                }
            }
        }

        throw new Exception('No image data found in response');
    }

    /**
     * Update API key
     */
    public function update_api_key($api_key) {
        $this->api_key = $api_key;
    }

    /**
     * Get available models
     */
    public function get_available_models() {
        if (empty($this->api_key)) {
            throw new Exception('Google AI API key not configured');
        }

        $api_url = 'https://generativelanguage.googleapis.com/v1beta/models?key=' . $this->api_key;

        $response = wp_remote_get($api_url, array(
            'timeout' => 10
        ));

        if (is_wp_error($response)) {
            throw new Exception('Connection failed: ' . $response->get_error_message());
        }

        $body = json_decode(wp_remote_retrieve_body($response), true);
        $response_code = wp_remote_retrieve_response_code($response);

        if ($response_code !== 200) {
            $error_msg = isset($body['error']['message']) ? $body['error']['message'] : 'Unknown error';
            throw new Exception("API Error ($response_code): $error_msg");
        }

        return $body['models'] ?? array();
    }

    /**
     * Check if image generation is available
     */
    public function is_image_generation_available() {
        try {
            $models = $this->get_available_models();
            foreach ($models as $model) {
                if (strpos($model['name'], 'image') !== false || strpos($model['displayName'], 'Image') !== false) {
                    return true;
                }
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }
}