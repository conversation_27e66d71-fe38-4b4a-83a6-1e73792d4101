<?php
/**
 * Database Manager for Elshadaifm Renungan API
 * Refactored for better structure and organization
 */

class Elshadaifm_Renungan_DB_Manager {

    private $table_name;
    private $detail_table_name;
    private $config;
    private $logger;

    public function __construct() {
        global $wpdb;
        $this->config = new Elshadaifm_Renungan_Config();
        $this->logger = new Elshadaifm_Renungan_Logger();

        $table_names = $this->config->get_table_names();
        $this->table_name = $table_names['main'];
        $this->detail_table_name = $table_names['detail'];
    }

    // Table Management
    public function create_tables() {
        try {
            $this->logger->log_database_operation('create', 'tables');

            global $wpdb;
            $charset_collate = $wpdb->get_charset_collate();

            $main_table_sql = $this->get_main_table_schema($charset_collate);
            $detail_table_sql = $this->get_detail_table_schema($charset_collate);

            $this->execute_table_creation($main_table_sql, $detail_table_sql);

            $this->config->set_db_version($this->config::DB_VERSION);
            $this->logger->log('Database tables created successfully', Elshadaifm_Renungan_Logger::INFO);
        } catch (Exception $e) {
            $this->logger->log_error('Failed to create database tables: ' . $e->getMessage());
            throw $e;
        }
    }

    // Content Management
    public function content_exists($content_hash) {
        global $wpdb;
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$this->table_name} WHERE content_hash = %s",
            $content_hash
        ));

        return !empty($existing);
    }

    public function save_renungan($content_hash, $content) {
        try {
            global $wpdb;
            $result = $wpdb->insert(
                $this->table_name,
                array(
                    'content_hash' => $content_hash,
                    'raw_content' => $content
                ),
                array('%s', '%s')
            );

            if ($result) {
                $this->logger->log_database_operation('insert', $this->table_name, true);
                return $wpdb->insert_id;
            } else {
                $this->logger->log_database_operation('insert', $this->table_name, false);
                return false;
            }
        } catch (Exception $e) {
            $this->logger->log_error('Failed to save renungan: ' . $e->getMessage());
            return false;
        }
    }

    public function get_pending_content() {
        global $wpdb;
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE status = 'pending' ORDER BY created_at ASC LIMIT 1"
        ));
    }

    public function update_content_status($content_id, $status) {
        global $wpdb;
        return $wpdb->update(
            $this->table_name,
            array('status' => $status),
            array('id' => $content_id),
            array('%s'),
            array('%d')
        );
    }

    // Details Management
    public function save_renungan_details($extracted_data) {
        global $wpdb;
        $inserted_ids = array();

        if ($this->is_multiple_entries($extracted_data)) {
            $inserted_ids = $this->save_multiple_entries($extracted_data);
        } else {
            $inserted_ids = $this->save_single_entry($extracted_data);
        }

        return !empty($inserted_ids) ? $inserted_ids : false;
    }

    public function get_pending_content_generation() {
        try {
            global $wpdb;
            $result = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$this->detail_table_name}
                 WHERE content_generated = %d
                 ORDER BY created ASC
                 LIMIT 1",
                false
            ));

            $this->logger->log_debug('Retrieved pending content generation: ' . ($result ? $result->id : 'none'));
            return $result;
        } catch (Exception $e) {
            $this->logger->log_error('Failed to get pending content generation: ' . $e->getMessage());
            return false;
        }
    }

    public function update_content_generation($id, $generated_content) {
        global $wpdb;
        return $wpdb->update(
            $this->detail_table_name,
            array(
                'content' => $generated_content,
                'content_generated' => true
            ),
            array('id' => $id),
            array('%s', '%d'),
            array('%d')
        );
    }

    public function get_pending_image_generation() {
        try {
            global $wpdb;
            $result = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$this->detail_table_name}
                 WHERE image_generated = %d AND content_generated = %d
                 ORDER BY created ASC
                 LIMIT 1",
                false, true
            ));

            $this->logger->log_debug('Retrieved pending image generation: ' . ($result ? $result->id : 'none'));
            return $result;
        } catch (Exception $e) {
            $this->logger->log_error('Failed to get pending image generation: ' . $e->getMessage());
            return false;
        }
    }

    public function update_image_generation($id, $attachment_id, $image_prompt) {
        try {
            global $wpdb;
            $result = $wpdb->update(
                $this->detail_table_name,
                array(
                    'image_generated' => true,
                    'attachment_id' => $attachment_id,
                    'image_prompt' => $image_prompt
                ),
                array('id' => $id),
                array('%d', '%d', '%s'),
                array('%d')
            );

            if ($result !== false) {
                $this->logger->log_database_operation('update', $this->detail_table_name, true);
                $this->logger->log_content_processing($id, 'image_generation_completed');
            } else {
                $this->logger->log_database_operation('update', $this->detail_table_name, false);
            }

            return $result;
        } catch (Exception $e) {
            $this->logger->log_error('Failed to update image generation: ' . $e->getMessage());
            return false;
        }
    }

    // Data Retrieval
    public function get_all_renungan($limit = 10, $offset = 0) {
        try {
            global $wpdb;
            $results = $wpdb->get_results($wpdb->prepare(
                "SELECT id, LEFT(content_hash, 10) as hash_prefix, status, created_at
                 FROM {$this->table_name}
                 ORDER BY created_at DESC
                 LIMIT %d OFFSET %d",
                $limit, $offset
            ));

            $this->logger->log_debug('Retrieved ' . count($results) . ' renungan records');
            return $results;
        } catch (Exception $e) {
            $this->logger->log_error('Failed to get all renungan: ' . $e->getMessage());
            return array();
        }
    }

    public function get_all_renungan_details($limit = 10, $offset = 0) {
        global $wpdb;
        return $wpdb->get_results($wpdb->prepare(
            "SELECT id, tanggal_publish, ayat, judul, LEFT(summary, 50) as summary_preview, created
             FROM {$this->detail_table_name}
             ORDER BY created DESC
             LIMIT %d OFFSET %d",
            $limit, $offset
        ));
    }

    public function get_renungan_by_status($status, $limit = 10, $offset = 0) {
        global $wpdb;
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$this->table_name}
             WHERE status = %s
             ORDER BY created_at DESC
             LIMIT %d OFFSET %d",
            $status, $limit, $offset
        ));
    }

    public function get_renungan_details_by_status($status, $limit = 10, $offset = 0) {
        global $wpdb;
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$this->detail_table_name}
             WHERE status = %s
             ORDER BY created_at DESC
             LIMIT %d OFFSET %d",
            $status, $limit, $offset
        ));
    }

    // Private Methods - Table Schema
    private function get_main_table_schema($charset_collate) {
        return "CREATE TABLE IF NOT EXISTS {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            content_hash varchar(64) NOT NULL,
            raw_content longtext NOT NULL,
            status varchar(20) DEFAULT 'pending',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            UNIQUE KEY content_hash (content_hash),
            KEY status (status)
        ) $charset_collate;";
    }

    private function get_detail_table_schema($charset_collate) {
        return "CREATE TABLE IF NOT EXISTS {$this->detail_table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            created datetime DEFAULT CURRENT_TIMESTAMP,
            tanggal_publish date DEFAULT NULL,
            ayat text DEFAULT NULL,
            judul varchar(255) DEFAULT NULL,
            summary text DEFAULT NULL,
            content longtext DEFAULT NULL,
            status varchar(20) DEFAULT 'pending',
            content_generated boolean DEFAULT FALSE,
            image_generated boolean DEFAULT FALSE,
            insert_to_wp boolean DEFAULT FALSE,
            attachment_id bigint(20) DEFAULT NULL,
            image_prompt text DEFAULT NULL,
            PRIMARY KEY  (id),
            KEY tanggal_publish (tanggal_publish),
            KEY status (status)
        ) $charset_collate;";
    }

    private function execute_table_creation($main_table_sql, $detail_table_sql) {
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($main_table_sql);
        dbDelta($detail_table_sql);
    }

    // Private Methods - Details Processing
    private function is_multiple_entries($extracted_data) {
        return is_array($extracted_data) && isset($extracted_data[0]);
    }

    private function save_multiple_entries($extracted_data) {
        global $wpdb;
        $inserted_ids = array();

        foreach ($extracted_data as $entry) {
            $result = $wpdb->insert(
                $this->detail_table_name,
                $this->prepare_entry_data($entry),
                $this->get_entry_format()
            );

            if ($result) {
                $inserted_ids[] = $wpdb->insert_id;
            }
        }

        return $inserted_ids;
    }

    private function save_single_entry($extracted_data) {
        global $wpdb;
        $inserted_ids = array();

        $result = $wpdb->insert(
            $this->detail_table_name,
            $this->prepare_entry_data($extracted_data),
            $this->get_entry_format()
        );

        if ($result) {
            $inserted_ids[] = $wpdb->insert_id;
        }

        return $inserted_ids;
    }

    private function prepare_entry_data($entry) {
        return array(
            'tanggal_publish' => isset($entry['tanggal_publish']) ? $entry['tanggal_publish'] : null,
            'ayat' => isset($entry['ayat']) ? $entry['ayat'] : null,
            'judul' => isset($entry['judul']) ? $entry['judul'] : null,
            'summary' => isset($entry['summary']) ? $entry['summary'] : null,
            'content_generated' => false,
            'image_generated' => false,
            'insert_to_wp' => false
        );
    }

    private function get_entry_format() {
        return array('%s', '%s', '%s', '%s', '%d', '%d', '%d');
    }
}