<?php
/**
 * Broadcast Schedule Section Template
 * ElshadaiFM Theme - Radio Station Schedule Display
 * 
 * @package ElshadaiFM
 */

// Get current time and day for schedule status detection - force GMT+7 Jakarta timezone
date_default_timezone_set('Asia/Jakarta');
$current_time = current_time('timestamp');
$current_day_of_week = date('w', $current_time); // 0 (Sunday) to 6 (Saturday)
$today_date = date('Y-m-d', $current_time);

// Function to get broadcast schedule data from plugin
function get_broadcast_schedule_data($limit = 6) {
    global $wpdb;

    // Ensure GMT+7 Jakarta timezone
    date_default_timezone_set('Asia/Jakarta');

    $table_name = $wpdb->prefix . 'efbs_schedules';
    $current_time = current_time('timestamp');
    $current_day_of_week = date('w', $current_time);
    $today_date = date('Y-m-d', $current_time);
    $current_time_str = date('H:i:s', $current_time);
    
    $broadcast_schedule = array();
    
    // Get today's regular schedules
    $regular_schedules = $wpdb->get_results($wpdb->prepare("
        SELECT s.*, p.post_title as program_title, p.post_content as program_description,
               b.post_title as broadcaster_name, b.ID as broadcaster_id
        FROM $table_name s
        LEFT JOIN {$wpdb->posts} p ON s.post_id = p.ID
        LEFT JOIN {$wpdb->posts} b ON s.broadcaster_id = b.ID
        WHERE s.status = 'active' 
        AND s.is_regular = 1
        AND s.day_of_week = %d
        AND p.post_status = 'publish'
        ORDER BY s.start_time ASC
    ", $current_day_of_week));
    
    // Get today's special events
    $special_schedules = $wpdb->get_results($wpdb->prepare("
        SELECT s.*, p.post_title as program_title, p.post_content as program_description,
               b.post_title as broadcaster_name, b.ID as broadcaster_id
        FROM $table_name s
        LEFT JOIN {$wpdb->posts} p ON s.post_id = p.ID
        LEFT JOIN {$wpdb->posts} b ON s.broadcaster_id = b.ID
        WHERE s.status = 'active' 
        AND s.is_regular = 0
        AND s.specific_date = %s
        AND p.post_status = 'publish'
        ORDER BY s.start_time ASC
    ", $today_date));
    
    // Merge and sort all schedules
    $all_schedules = array_merge($regular_schedules, $special_schedules);
    
    // Sort by start time
    usort($all_schedules, function($a, $b) {
        return strcmp($a->start_time, $b->start_time);
    });
    
    // Convert to display format
    foreach ($all_schedules as $schedule) {
        $start_time = strtotime($schedule->start_time);
        $end_time = strtotime($schedule->end_time);
        $current_timestamp = strtotime($current_time_str);

        // Skip schedules that have already ended
        if ($current_timestamp > $end_time) {
            continue;
        }

        // Determine status
        $status = 'upcoming';
        $is_live = false;

        if ($current_timestamp >= $start_time && $current_timestamp <= $end_time) {
            $status = 'live';
            $is_live = true;
        }

        // Determine program type based on title or content
        $type = 'general';
        $title_lower = strtolower($schedule->program_title);
        if (strpos($title_lower, 'doa') !== false || strpos($title_lower, 'prayer') !== false) {
            $type = 'prayer';
        } elseif (strpos($title_lower, 'worship') !== false || strpos($title_lower, 'penyembahan') !== false || strpos($title_lower, 'subuh') !== false) {
            $type = 'worship';
        } elseif (strpos($title_lower, 'music') !== false || strpos($title_lower, 'gospel') !== false || strpos($title_lower, 'lagu') !== false) {
            $type = 'music';
        } elseif (strpos($title_lower, 'youth') !== false || strpos($title_lower, 'pemuda') !== false || strpos($title_lower, 'generasi') !== false) {
            $type = 'youth';
        } elseif (strpos($title_lower, 'ibadah') !== false || strpos($title_lower, 'service') !== false) {
            $type = 'service';
        }

        $broadcast_schedule[] = array(
            'id' => $schedule->id,
            'title' => $schedule->program_title,
            'host' => $schedule->broadcaster_name ?: 'Tim ElshadaiFM',
            'time' => date('H:i', $start_time) . ' - ' . date('H:i', $end_time),
            'time_24h' => date('G.i', $start_time) . '-' . date('G.i', $end_time),
            'day' => $is_live ? 'LIVE SEKARANG' : 'Hari Ini',
            'type' => $type,
            'description' => $schedule->program_description ?: 'Program siaran ElshadaiFM',
            'status' => $status,
            'live_indicator' => $is_live
        );
    }
    
    // If no schedules found, add upcoming schedules from next days
    if (empty($broadcast_schedule)) {
        for ($i = 1; $i <= 7; $i++) {
            $next_day = ($current_day_of_week + $i) % 7;
            $next_date = date('Y-m-d', strtotime("+$i days"));
            
            // Get regular schedules for next day
            $next_regular = $wpdb->get_results($wpdb->prepare("
                SELECT s.*, p.post_title as program_title, p.post_content as program_description,
                       b.post_title as broadcaster_name, b.ID as broadcaster_id
                FROM $table_name s
                LEFT JOIN {$wpdb->posts} p ON s.post_id = p.ID
                LEFT JOIN {$wpdb->posts} b ON s.broadcaster_id = b.ID
                WHERE s.status = 'active' 
                AND s.is_regular = 1
                AND s.day_of_week = %d
                AND p.post_status = 'publish'
                ORDER BY s.start_time ASC
                LIMIT 2
            ", $next_day));
            
            foreach ($next_regular as $schedule) {
                $start_time = strtotime($schedule->start_time);
                $end_time = strtotime($schedule->end_time);
                
                $day_names = array('Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu');
                $day_name = $day_names[$next_day];
                
                $type = 'general';
                $title_lower = strtolower($schedule->program_title);
                if (strpos($title_lower, 'doa') !== false) $type = 'prayer';
                elseif (strpos($title_lower, 'worship') !== false || strpos($title_lower, 'subuh') !== false) $type = 'worship';
                elseif (strpos($title_lower, 'music') !== false || strpos($title_lower, 'gospel') !== false) $type = 'music';
                elseif (strpos($title_lower, 'youth') !== false || strpos($title_lower, 'generasi') !== false) $type = 'youth';
                elseif (strpos($title_lower, 'ibadah') !== false) $type = 'service';
                
                $broadcast_schedule[] = array(
                    'id' => $schedule->id,
                    'title' => $schedule->program_title,
                    'host' => $schedule->broadcaster_name ?: 'Tim ElshadaiFM',
                    'time' => date('H:i', $start_time) . ' - ' . date('H:i', $end_time),
                    'time_24h' => date('G.i', $start_time) . '-' . date('G.i', $end_time),
                    'day' => $day_name,
                    'type' => $type,
                    'description' => $schedule->program_description ?: 'Program siaran ElshadaiFM',
                    'status' => 'scheduled',
                    'live_indicator' => false
                );
                
                if (count($broadcast_schedule) >= $limit) break 2;
            }
        }
    }
    
    return array_slice($broadcast_schedule, 0, $limit);
}

// Get the broadcast schedule data - ambil lebih banyak untuk scroll
$broadcast_schedule = get_broadcast_schedule_data(10);

// Debug: Tampilkan waktu saat ini untuk verifikasi
$current_debug_time = date('Y-m-d H:i:s');
$current_day_debug = date('l');
?>
<!-- Debug Info ( waktu: <?php echo $current_debug_time; ?>, hari: <?php echo $current_day_debug; ?>, timezone: Asia/Jakarta ) -->

<section class="broadcast-schedule-section" id="broadcast-schedule">
    <!-- Animated background elements -->
    <div class="schedule-background-elements">
        <div class="radio-wave wave-1"></div>
        <div class="radio-wave wave-2"></div>
        <div class="radio-wave wave-3"></div>
    </div>
    
    <div class="container">

        <!-- Upcoming Shows Grid -->
        <div class="upcoming-shows-section">
            <h3 class="upcoming-title">
                <span class="upcoming-accent">JADWAL</span> SIARAN
            </h3>

            <div class="shows-container-outer">
                <!-- Navigation Arrows Outside Container -->
                <button class="schedule-arrow prev-arrow" id="schedulePrev" aria-label="Jadwal sebelumnya">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M15 18l-6-6 6-6"/>
                    </svg>
                </button>

                <div class="shows-container">
                    <div class="shows-wrapper">
                    <div class="shows-scroll-container">
                        <div class="shows-grid" id="showsGrid">
                    <?php foreach($broadcast_schedule as $show): ?>
                        <div class="show-card <?php echo $show['type']; ?> <?php echo $show['status']; ?>">
                        

                        <!-- Show Content -->
                        <div class="show-content">
                            <div class="show-time">
                                <span class="time-display"><?php echo $show['time']; ?></span>
                            </div>
                            
                            <h4 class="show-title"><?php echo $show['title']; ?></h4>
                            <p class="show-description"><?php echo $show['description']; ?></p>
                            <div class="show-status-badge">
                                <?php if ($show['live_indicator']): ?>
                                    <span class="live-now-badge">LIVE NOW</span>
                                <?php else: ?>
                                    <span class="today-badge">HARI INI</span>
                                <?php endif; ?>
                            </div>
                            
                        </div>

                        <!-- Interactive Hover Effects -->
                        <div class="card-glow-effect"></div>
                    </div>
                    <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                </div>

                <button class="schedule-arrow next-arrow" id="scheduleNext" aria-label="Jadwal berikutnya">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 18l6-6-6-6"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Testimonials Section within Broadcast Schedule -->
        <div class="testimonials-within-broadcast">
            <?php
            // Get testimonials data - reuse function from testimonials-functions.php
            function get_popular_testimonials_inline($limit = 5) {
                // Try to get from custom post type first
                $args = array(
                    'post_type' => 'testimonial',
                    'posts_per_page' => $limit,
                    'post_status' => 'publish',
                    'meta_key' => 'featured',
                    'meta_value' => 'yes',
                    'orderby' => 'date',
                    'order' => 'DESC'
                );
                
                $testimonials = get_posts($args);
                
                // If no featured testimonials, get latest testimonials
                if (empty($testimonials)) {
                    $args['meta_query'] = array(); // Remove meta query
                    unset($args['meta_key'], $args['meta_value']);
                    $testimonials = get_posts($args);
                }
                
                // If still no testimonials from custom post type, create sample data
                if (empty($testimonials)) {
                    return array(
                        array(
                            'content' => 'Radio ElshadaiFM telah menjadi berkat besar dalam hidup saya. Setiap pagi, saya mendengarkan program "Subuh Berkat" yang selalu memberikan kekuatan dan inspirasi untuk memulai hari.',
                            'author' => 'Sarah Wijaya',
                            'location' => 'Jakarta',
                            'rating' => 5
                        ),
                        array(
                            'content' => 'Program "Youth Connect" sangat memberkati kaum muda seperti saya. Musik gospel yang fresh dan sharing yang inspiratif membuat iman saya semakin kuat.',
                            'author' => 'David Pratama',
                            'location' => 'Bandung',
                            'rating' => 5
                        ),
                        array(
                            'content' => 'Sebagai ibu rumah tangga, saya sangat terbantu dengan program doa pagi. Tim ElshadaiFM benar-benar memahami kebutuhan rohani pendengarnya.',
                            'author' => 'Maria Sari',
                            'location' => 'Surabaya',
                            'rating' => 5
                        ),
                        array(
                            'content' => 'Kualitas siaran yang jernih dan program yang berkualitas membuat ElshadaiFM menjadi pilihan utama untuk mendengarkan radio Kristen.',
                            'author' => 'Pastor John',
                            'location' => 'Medan',
                            'rating' => 5
                        ),
                        array(
                            'content' => 'Terima kasih ElshadaiFM sudah menjadi sahabat setia dalam perjalanan rohani keluarga kami. Tuhan memberkati pelayanan ini.',
                            'author' => 'Keluarga Santoso',
                            'location' => 'Yogyakarta',
                            'rating' => 5
                        )
                    );
                }
                
                // Convert WordPress posts to array format
                $testimonial_data = array();
                foreach ($testimonials as $testimonial) {
                    $author = get_post_meta($testimonial->ID, 'author_name', true) ?: $testimonial->post_title;
                    $location = get_post_meta($testimonial->ID, 'author_location', true) ?: 'Indonesia';
                    $rating = get_post_meta($testimonial->ID, 'rating', true) ?: 5;
                    
                    $testimonial_data[] = array(
                        'content' => $testimonial->post_content,
                        'author' => $author,
                        'location' => $location,
                        'rating' => intval($rating)
                    );
                }
                
                return $testimonial_data;
            }
            
            $testimonials = get_popular_testimonials_inline(5);
            ?>
            
            <div class="testimonial-broadcast-wrapper">
                <div class="testimonial-display-broadcast">
                    <div class="testimonial-container-broadcast">
                        <?php foreach ($testimonials as $index => $testimonial): ?>
                            <div class="testimonial-item-broadcast <?php echo $index === 0 ? 'active' : ''; ?>">
                                <div class="testimonial-content-broadcast">
                                    <div class="quote-icon-broadcast">
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M14,17H17L19,13V7H13V13H16M6,17H9L11,13V7H5V13H8L6,17Z" />
                                        </svg>
                                    </div>
                                    
                                    <p class="testimonial-text-broadcast">
                                        <?php echo esc_html($testimonial['content']); ?>
                                    </p>
                                    
                                    <div class="testimonial-author-broadcast">
                                        <div class="author-info-broadcast">
                                            <h4 class="author-name-broadcast"><?php echo esc_html($testimonial['author']); ?></h4>
                                            <p class="author-location-broadcast"><?php echo esc_html($testimonial['location']); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="testimonial-indicators-broadcast">
                    <?php foreach ($testimonials as $index => $testimonial): ?>
                        <button class="indicator-broadcast <?php echo $index === 0 ? 'active' : ''; ?>" 
                                data-testimonial="<?php echo $index; ?>" 
                                aria-label="Show testimonial <?php echo $index + 1; ?>">
                        </button>
                    <?php endforeach; ?>
                </div>
                
                <div class="testimonials-cta-broadcast">
                    <a href="/testimonials" class="view-testimonials-btn-broadcast">
                        <span>Lihat Semua Testimoni</span>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M5 12h14M12 5l7 7-7 7"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>

    </div>
</section>