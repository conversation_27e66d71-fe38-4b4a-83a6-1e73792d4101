<?php
/**
 * Admin Pages for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class EfraI_Admin_Pages {

    private $main_plugin;

    public function __construct($main_plugin) {
        $this->main_plugin = $main_plugin;
    }

    /**
     * Add admin menu items
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Renungan AI', 'elshadaifm-renungan-ai'),
            __('Renungan AI', 'elshadaifm-renungan-ai'),
            'manage_options',
            'efrai-dashboard',
            array($this, 'render_dashboard_page'),
            'dashicons-ai',
            6
        );

        add_submenu_page(
            'efrai-dashboard',
            __('Generate Renungan', 'elshadaifm-renungan-ai'),
            __('Generate Renungan', 'elshadaifm-renungan-ai'),
            'edit_posts',
            'efrai-generate',
            array($this, 'render_generate_page')
        );

        add_submenu_page(
            'efrai-dashboard',
            __('Settings', 'elshadaifm-renungan-ai'),
            __('Settings', 'elshadaifm-renungan-ai'),
            'manage_options',
            'efrai-settings',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Register admin settings
     */
    public function register_settings() {
        register_setting('efrai_settings_group', 'efrai_openai_api_key');
        register_setting('efrai_settings_group', 'efrai_openai_model');
        register_setting('efrai_settings_group', 'efrai_google_ai_key');

        add_settings_section(
            'efrai_settings_section',
            __('API Configuration', 'elshadaifm-renungan-ai'),
            array($this, 'settings_section_callback'),
            'efrai-settings'
        );

        add_settings_field(
            'efrai_openai_api_key',
            __('OpenAI API Key', 'elshadaifm-renungan-ai'),
            array($this, 'openai_api_key_callback'),
            'efrai-settings',
            'efrai_settings_section'
        );

        add_settings_field(
            'efrai_openai_model',
            __('OpenAI Model', 'elshadaifm-renungan-ai'),
            array($this, 'openai_model_callback'),
            'efrai-settings',
            'efrai_settings_section'
        );

        add_settings_field(
            'efrai_google_ai_key',
            __('Google AI API Key', 'elshadaifm-renungan-ai'),
            array($this, 'google_ai_key_callback'),
            'efrai-settings',
            'efrai_settings_section'
        );
    }

    /**
     * Settings section callback
     */
    public function settings_section_callback() {
        echo '<p>' . __('Configure your API keys for AI services.', 'elshadaifm-renungan-ai') . '</p>';
    }

    /**
     * OpenAI API key field callback
     */
    public function openai_api_key_callback() {
        $value = get_option('efrai_openai_api_key', '');
        echo '<input type="password" name="efrai_openai_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Enter your OpenAI API key. You can get it from https://platform.openai.com/api-keys', 'elshadaifm-renungan-ai') . '</p>';
    }

    /**
     * OpenAI model field callback
     */
    public function openai_model_callback() {
        $value = get_option('efrai_openai_model', 'gpt-4o-mini');
        echo '<input type="hidden" name="efrai_openai_model" value="gpt-4o-mini" />';
        echo '<strong>GPT-4o Mini</strong>';
        echo '<p class="description">' . __('Using GPT-4o Mini for fast and efficient text generation', 'elshadaifm-renungan-ai') . '</p>';
    }

    /**
     * Google AI key field callback
     */
    public function google_ai_key_callback() {
        $value = get_option('efrai_google_ai_key', '');
        echo '<input type="password" name="efrai_google_ai_key" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Enter your Google AI API key for image generation using Gemini 2.5 Flash Image Preview. Get it from https://makersuite.google.com/app/apikey', 'elshadaifm-renungan-ai') . '</p>';
    }

    /**
     * Render dashboard page
     */
    public function render_dashboard_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Renungan AI Dashboard', 'elshadaifm-renungan-ai'); ?></h1>

            <div class="efrai-dashboard-container">
                <div class="efrai-dashboard-section">
                    <h2><?php echo esc_html__('Quick Stats', 'elshadaifm-renungan-ai'); ?></h2>
                    <?php
                    $total_renungan = wp_count_posts('renungan')->publish;
                    $latest_renungan = $this->main_plugin->get_utils()->get_latest_renungan(1);
                    ?>
                    <ul>
                        <li><?php printf(__('Total Published Renungan: %d', 'elshadaifm-renungan-ai'), $total_renungan); ?></li>
                        <li><?php echo !empty($latest_renungan) ? sprintf(__('Latest: %s', 'elshadaifm-renungan-ai'), $latest_renungan[0]->post_title) : __('No renungan yet', 'elshadaifm-renungan-ai'); ?></li>
                    </ul>
                </div>

                <div class="efrai-dashboard-section">
                    <h2><?php echo esc_html__('Quick Actions', 'elshadaifm-renungan-ai'); ?></h2>
                    <p>
                        <a href="<?php echo admin_url('admin.php?page=efrai-generate'); ?>" class="button button-primary">
                            <?php echo esc_html__('Generate New Renungan', 'elshadaifm-renungan-ai'); ?>
                        </a>
                        <a href="<?php echo admin_url('post-new.php?post_type=renungan'); ?>" class="button">
                            <?php echo esc_html__('Create Manual Renungan', 'elshadaifm-renungan-ai'); ?>
                        </a>
                    </p>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render generate page
     */
    public function render_generate_page() {
        require_once EFRAI_PLUGIN_PATH . 'admin/generate-page.php';
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        require_once EFRAI_PLUGIN_PATH . 'admin/settings-page.php';
    }

    /**
     * Add settings link to plugins page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=efrai-settings') . '">' . __('Settings', 'elshadaifm-renungan-ai') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
}