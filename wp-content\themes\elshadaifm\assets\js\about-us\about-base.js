/* ==========================================================================
   ABOUT US - BASE FUNCTIONALITY & UTILITIES
   ========================================================================== */

/**
 * About Us Base Module
 * Handles basic setup, utilities, and common functionality
 */
window.AboutUsBase = (function() {
    'use strict';

    // Private variables
    let isInitialized = false;
    let isMobile = false;

    // Public API
    const api = {
        init: init,
        isMobile: () => isMobile,
        isInitialized: () => isInitialized,
        utils: {
            debounce: debounce,
            throttle: throttle,
            isElementInViewport: isElementInViewport,
            addClass: addClass,
            removeClass: removeClass,
            hasClass: hasClass
        }
    };

    /**
     * Initialize base functionality
     */
    function init() {
        if (isInitialized) return;

        console.log('AboutUsBase: Initializing...');

        // Add 'js' class to body to enable JavaScript-dependent styles
        document.body.classList.add('js');

        // Detect mobile
        detectMobile();

        // Setup smooth scrolling
        setupSmoothScrolling();

        // Setup keyboard navigation
        setupKeyboardNavigation();

        // Setup error handling
        setupErrorHandling();

        isInitialized = true;
        console.log('AboutUsBase: Initialized successfully');
    }

    /**
     * Detect if device is mobile
     */
    function detectMobile() {
        isMobile = window.innerWidth <= 768;
        
        // Update on resize
        window.addEventListener('resize', debounce(() => {
            isMobile = window.innerWidth <= 768;
        }, 250));
    }

    /**
     * Setup smooth scrolling for anchor links
     */
    function setupSmoothScrolling() {
        // Add smooth scroll behavior
        if ('scrollBehavior' in document.documentElement.style) {
            document.documentElement.style.scrollBehavior = 'smooth';
        }

        // Handle anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Setup keyboard navigation
     */
    function setupKeyboardNavigation() {
        document.addEventListener('keydown', function(e) {
            // Skip to content with Alt + S
            if (e.altKey && e.key === 's') {
                const mainContent = document.querySelector('#main');
                if (mainContent) {
                    mainContent.focus();
                    mainContent.scrollIntoView({ behavior: 'smooth' });
                }
            }

            // Navigate sections with arrow keys
            if (e.key === 'ArrowDown' && e.ctrlKey) {
                const currentSection = document.querySelector('section:focus') || document.querySelector('section');
                if (currentSection) {
                    const nextSection = currentSection.nextElementSibling;
                    if (nextSection && nextSection.tagName === 'SECTION') {
                        nextSection.focus();
                        nextSection.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            }
        });
    }

    /**
     * Setup global error handling
     */
    function setupErrorHandling() {
        window.addEventListener('error', function(e) {
            console.warn('AboutUs Error:', e.error);
        });
    }

    /**
     * Debounce function
     */
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    /**
     * Throttle function
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Check if element is in viewport
     */
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    /**
     * Add class utility
     */
    function addClass(element, className) {
        if (element && className) {
            element.classList.add(className);
        }
    }

    /**
     * Remove class utility
     */
    function removeClass(element, className) {
        if (element && className) {
            element.classList.remove(className);
        }
    }

    /**
     * Has class utility
     */
    function hasClass(element, className) {
        return element && element.classList.contains(className);
    }

    return api;
})();
