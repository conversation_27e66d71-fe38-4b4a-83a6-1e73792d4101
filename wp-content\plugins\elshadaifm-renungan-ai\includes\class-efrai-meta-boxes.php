<?php
/**
 * Meta Box Functionality for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class EfraI_Meta_Boxes {

    private $utils;

    public function __construct($utils) {
        $this->utils = $utils;
    }

    /**
     * Add meta boxes for renungan post type
     */
    public function add_meta_boxes() {
        add_meta_box(
            'efrai_renungan_details',
            __('Renungan Details', 'elshadaifm-renungan-ai'),
            array($this, 'render_meta_box'),
            'renungan',
            'normal',
            'high'
        );
    }

    /**
     * Render meta box content
     */
    public function render_meta_box($post) {
        wp_nonce_field('efrai_renungan_meta_box', 'efrai_meta_box_nonce');

        $tanggal = get_post_meta($post->ID, '_efrai_tanggal', true);
        $ayat = get_post_meta($post->ID, '_efrai_ayat', true);
        $tema = get_post_meta($post->ID, '_efrai_tema', true);
        ?>
        <div class="efrai-meta-box">
            <p>
                <label for="efrai_tanggal"><?php echo esc_html__('Tanggal:', 'elshadaifm-renungan-ai'); ?></label>
                <input type="date" id="efrai_tanggal" name="efrai_tanggal"
                       value="<?php echo esc_attr($tanggal); ?>" class="regular-text">
            </p>
            <p>
                <label for="efrai_ayat"><?php echo esc_html__('Ayat:', 'elshadaifm-renungan-ai'); ?></label>
                <input type="text" id="efrai_ayat" name="efrai_ayat"
                       value="<?php echo esc_attr($ayat); ?>" class="regular-text">
            </p>
            <p>
                <label for="efrai_tema"><?php echo esc_html__('Tema:', 'elshadaifm-renungan-ai'); ?></label>
                <input type="text" id="efrai_tema" name="efrai_tema"
                       value="<?php echo esc_attr($tema); ?>" class="regular-text">
            </p>
            <?php if ($this->utils->has_ai_image($post->ID)) : ?>
                <p>
                    <strong><?php echo esc_html__('AI Generated Image:', 'elshadaifm-renungan-ai'); ?></strong>
                    <span class="efrai-ai-image-indicator" style="color: #28a745;">
                        ✓ <?php echo esc_html__('Generated', 'elshadaifm-renungan-ai'); ?>
                    </span>
                </p>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Save meta box data
     */
    public function save_meta_box($post_id) {
        if (!isset($_POST['efrai_meta_box_nonce'])) {
            return;
        }

        if (!wp_verify_nonce($_POST['efrai_meta_box_nonce'], 'efrai_renungan_meta_box')) {
            return;
        }

        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        if (isset($_POST['efrai_tanggal'])) {
            update_post_meta($post_id, '_efrai_tanggal', sanitize_text_field($_POST['efrai_tanggal']));
        }

        if (isset($_POST['efrai_ayat'])) {
            update_post_meta($post_id, '_efrai_ayat', sanitize_text_field($_POST['efrai_ayat']));
        }

        if (isset($_POST['efrai_tema'])) {
            update_post_meta($post_id, '_efrai_tema', sanitize_text_field($_POST['efrai_tema']));
        }
    }
}