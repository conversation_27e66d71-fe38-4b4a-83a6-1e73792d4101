<?php
/**
 * Debug script for testing Google AI connection
 * This is a standalone script to test the API connection
 */

// Include WordPress
require_once('../../../../wp-config.php');

// Get API key from options
$google_ai_key = get_option('efrai_google_ai_key', '');

if (empty($google_ai_key)) {
    die('Google AI API key not configured. Please add it in Settings → Renungan AI Settings');
}

echo "Testing Google AI Connection...\n";
echo "API Key: " . substr($google_ai_key, 0, 10) . "...\n\n";

// Test 1: List models (simple test)
echo "1. Testing model listing...\n";
$api_url = 'https://generativelanguage.googleapis.com/v1beta/models?key=' . $google_ai_key;

$response = wp_remote_get($api_url, array(
    'timeout' => 10,
));

if (is_wp_error($response)) {
    echo "ERROR: " . $response->get_error_message() . "\n";
} else {
    $body = json_decode(wp_remote_retrieve_body($response), true);
    $response_code = wp_remote_retrieve_response_code($response);

    echo "Response Code: " . $response_code . "\n";

    if ($response_code === 200) {
        if (isset($body['models']) && !empty($body['models'])) {
            echo "SUCCESS: Found " . count($body['models']) . " models\n";

            // Find image generation models
            $image_models = array();
            foreach ($body['models'] as $model) {
                if (strpos($model['name'], 'image') !== false || strpos($model['displayName'], 'Image') !== false) {
                    $image_models[] = $model['name'];
                }
            }

            if (!empty($image_models)) {
                echo "Image generation models found:\n";
                foreach ($image_models as $model) {
                    echo "  - $model\n";
                }
            }
        } else {
            echo "ERROR: No models found in response\n";
        }
    } else {
        $error_msg = isset($body['error']['message']) ? $body['error']['message'] : 'Unknown error';
        echo "ERROR: $error_msg\n";
    }
}

echo "\n";

// Test 2: Try image generation (if we have the right model)
echo "2. Testing image generation...\n";
$test_model = 'gemini-2.5-flash-image-preview';
$image_api_url = 'https://generativelanguage.googleapis.com/v1beta/models/' . $test_model . ':generateContent';

$response = wp_remote_post($image_api_url, array(
    'headers' => array(
        'Content-Type' => 'application/json',
        'x-goog-api-key' => $google_ai_key,
    ),
    'body' => json_encode(array(
        'contents' => array(
            array(
                'parts' => array(
                    array(
                        'text' => 'Generate a simple Christian cross image'
                    )
                )
            )
        ),
        'generationConfig' => array(
            'responseModalities' => array('Image')
        )
    )),
    'timeout' => 30,
));

if (is_wp_error($response)) {
    echo "ERROR: " . $response->get_error_message() . "\n";
} else {
    $body = json_decode(wp_remote_retrieve_body($response), true);
    $response_code = wp_remote_retrieve_response_code($response);

    echo "Response Code: " . $response_code . "\n";

    if ($response_code === 200) {
        if (isset($body['candidates'][0]['content']['parts'])) {
            $image_found = false;
            foreach ($body['candidates'][0]['content']['parts'] as $part) {
                if (isset($part['inline_data']['data'])) {
                    echo "SUCCESS: Image generated!\n";
                    echo "Image size: " . strlen($part['inline_data']['data']) . " characters\n";
                    echo "MIME type: " . ($part['inline_data']['mime_type'] ?? 'unknown') . "\n";
                    $image_found = true;
                    break;
                }
            }

            if (!$image_found) {
                echo "ERROR: No image data found in response\n";
                echo "Response structure:\n";
                print_r($body);
            }
        } else {
            echo "ERROR: Invalid response structure\n";
            print_r($body);
        }
    } else {
        $error_msg = isset($body['error']['message']) ? $body['error']['message'] : 'Unknown error';
        echo "ERROR: $error_msg\n";

        if (isset($body['error'])) {
            echo "Full error details:\n";
            print_r($body['error']);
        }
    }
}

echo "\nTest completed!\n";