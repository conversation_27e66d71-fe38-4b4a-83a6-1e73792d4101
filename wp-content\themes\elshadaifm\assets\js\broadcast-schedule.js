/*
 * Broadcast Schedule Interactive Functionality
 * ElshadaiFM Theme - Dynamic Radio Station Experience
 */

jQuery(document).ready(function($) {
    'use strict';

    // Initialize broadcast schedule functionality
    initBroadcastSchedule();

    function initBroadcastSchedule() {
        // Initialize schedule navigation
        initScheduleNavigation();
        
        // Initialize show card interactions
        initShowCardInteractions();
        
        // Initialize audio visualizer animation
        initAudioVisualizer();
        
        // Initialize time updates
        initTimeUpdates();
        
        // Initialize scroll animations
        initScrollAnimations();
        
        // Initialize notification system
        initNotificationSystem();
    }

    // Schedule Navigation Functionality
    function initScheduleNavigation() {
        $('.nav-btn').on('click', function(e) {
            e.preventDefault();

            const $btn = $(this);
            const dayFilter = $btn.data('day');

            // Update active state
            $('.nav-btn').removeClass('active');
            $btn.addClass('active');

            // Filter shows based on selected day
            filterShowsByDay(dayFilter);

            // Add button click animation
            $btn.addClass('clicked');
            setTimeout(() => {
                $btn.removeClass('clicked');
            }, 200);
        });

        // Initialize arrow navigation for horizontal scroll
        initArrowNavigation();
    }

    // Arrow Navigation for Horizontal Scroll
    function initArrowNavigation() {
        const $showsGrid = $('#showsGrid');
        const $showsScrollContainer = $showsGrid.closest('.shows-scroll-container');
        const $showsWrapper = $showsGrid.closest('.shows-wrapper');
        const $prevArrow = $('#schedulePrev');
        const $nextArrow = $('#scheduleNext');

        if (!$showsGrid.length || !$showsScrollContainer.length || !$showsWrapper.length || !$prevArrow.length || !$nextArrow.length) {
            return;
        }

        // Scroll amount for each click (card width + gap)
        const scrollAmount = 350; // 320px card + 30px gap

        // Previous arrow click with infinite scroll
        $prevArrow.on('click', function() {
            const currentScroll = $showsScrollContainer.scrollLeft();
            const maxScroll = $showsGrid[0].scrollWidth - $showsWrapper[0].clientWidth;

            let newScroll;

            // Check if we're at or near the beginning for infinite scroll
            if (currentScroll <= scrollAmount) {
                // Go to end for infinite scroll (but only if not live show)
                const $firstCard = $showsGrid.find('.show-card').first();
                const hasLiveShow = $firstCard.hasClass('live');

                if (!hasLiveShow) {
                    newScroll = maxScroll;
                    console.log('Infinite scroll: going to end');
                } else {
                    // If there's a live show, don't allow infinite scroll to end
                    newScroll = 0;
                    console.log('Live show detected, keeping at beginning');
                }
            } else {
                // Normal scroll
                newScroll = Math.max(0, currentScroll - scrollAmount);
            }

            // Try direct scroll assignment first
            console.log('Setting scrollLeft to:', newScroll);
            $showsScrollContainer[0].scrollLeft = newScroll;
            console.log('Direct scroll result:', $showsScrollContainer[0].scrollLeft);

            updateArrowStates();
        });

        // Next arrow click with infinite scroll
        $nextArrow.on('click', function() {
            const currentScroll = $showsScrollContainer.scrollLeft();
            const maxScroll = $showsGrid[0].scrollWidth - $showsWrapper[0].clientWidth;

            let newScroll;

            // Check if we're at or near the end for infinite scroll
            if (currentScroll >= maxScroll - scrollAmount) {
                // Reset to beginning for infinite scroll
                newScroll = 0;
                console.log('Infinite scroll: resetting to beginning from', currentScroll, 'to', newScroll);
            } else {
                // Normal scroll
                newScroll = Math.min(maxScroll, currentScroll + scrollAmount);
                console.log('Normal scroll from', currentScroll, 'to', newScroll);
            }

            // Debug log
            console.log('Next arrow clicked:');
            console.log('Current scroll:', currentScroll);
            console.log('Max scroll:', maxScroll);
            console.log('New scroll:', newScroll);
            console.log('Scroll width:', $showsGrid[0].scrollWidth);
            console.log('Client width:', $showsGrid[0].clientWidth);

            // Try direct scroll assignment first
            console.log('Setting scrollLeft to:', newScroll);
            $showsScrollContainer[0].scrollLeft = newScroll;
            console.log('Direct scroll result:', $showsScrollContainer[0].scrollLeft);

            updateArrowStates();
        });

        // Update arrow states (enabled/disabled)
        function updateArrowStates() {
            const currentScroll = $showsScrollContainer.scrollLeft();
            const scrollWidth = $showsGrid[0].scrollWidth;
            const clientWidth = $showsWrapper[0].clientWidth;
            const maxScroll = scrollWidth - clientWidth;

            // Debug log
            console.log('Arrow states update:');
            console.log('Current scroll:', currentScroll);
            console.log('Scroll width:', scrollWidth);
            console.log('Client width (wrapper):', clientWidth);
            console.log('Max scroll:', maxScroll);
            console.log('Scroll container width:', $showsScrollContainer[0].clientWidth);
            console.log('Wrapper element:', $showsWrapper[0]);

            // Check if there's a live show (first card is live)
            const $firstCard = $showsGrid.find('.show-card').first();
            const hasLiveShow = $firstCard.hasClass('live');

            // For infinite scroll, enable previous arrow unless there's a live show at start
            const hasEnoughContent = maxScroll > 0;
            const atStart = currentScroll <= 0;
            const atEnd = currentScroll >= maxScroll;

            // Previous arrow: disable only if at start AND has live show OR no content to scroll
            $prevArrow.prop('disabled', (atStart && hasLiveShow) || !hasEnoughContent);

            // Next arrow: always enable if there's content to scroll (infinite scroll)
            $nextArrow.prop('disabled', !hasEnoughContent);
        }

        // Initial arrow state update
        updateArrowStates();

        // Update arrow states on scroll
        $showsScrollContainer.on('scroll', function() {
            updateArrowStates();
        });

        // Handle window resize
        $(window).on('resize', function() {
            updateArrowStates();
        });
    }

    // Filter shows by day
    function filterShowsByDay(day) {
        const $showCards = $('.show-card');
        
        $showCards.fadeOut(300, function() {
            // In a real implementation, this would filter actual data
            // For now, we'll simulate filtering with a delay
            setTimeout(() => {
                // Show relevant cards based on day filter
                switch(day) {
                    case 'today':
                        $showCards.filter('.upcoming, .live').fadeIn(400);
                        break;
                    case 'tomorrow':
                        $showCards.filter('.scheduled').fadeIn(400);
                        break;
                    case 'week':
                        $showCards.fadeIn(400);
                        break;
                    default:
                        $showCards.fadeIn(400);
                }
                
                // Trigger scroll animation again for newly shown cards
                animateVisibleCards();
            }, 200);
        });
    }

    // Show Card Interactions
    function initShowCardInteractions() {
        $('.show-card').each(function() {
            const $card = $(this);
            
            $card.on('mouseenter', function() {
                // Add hover sound effect (could be implemented with Web Audio API)
                addHoverEffect($card);
            });
            
            $card.on('mouseleave', function() {
                removeHoverEffect($card);
            });
            
            $card.on('click', function() {
                handleShowCardClick($card);
            });
        });
    }

    function addHoverEffect($card) {
        // Create ripple effect on hover
        const ripple = $('<div class="card-ripple"></div>');
        $card.append(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
        
        // Enhance glow effect
        $card.addClass('enhanced-glow');
    }

    function removeHoverEffect($card) {
        $card.removeClass('enhanced-glow');
    }

    function handleShowCardClick($card) {
        const showTitle = $card.find('.show-title').text();
        const showTime = $card.find('.time-display').text();
        
        // Show detailed information modal (simplified for demo)
        showScheduleModal(showTitle, showTime, $card);
        
        // Add click animation
        $card.addClass('card-clicked');
        setTimeout(() => {
            $card.removeClass('card-clicked');
        }, 300);
    }

    // Audio Visualizer Animation
    function initAudioVisualizer() {
        const $visualizer = $('.audio-visualizer');
        const $bars = $visualizer.find('.bar');
        
        // Create realistic audio visualization
        function animateAudioBars() {
            $bars.each(function(index) {
                const $bar = $(this);
                const randomHeight = Math.random() * 40 + 20; // Random height between 20-60px
                const animationDuration = Math.random() * 0.5 + 0.8; // Random duration 0.8-1.3s
                
                $bar.css({
                    'height': randomHeight + 'px',
                    'animation-duration': animationDuration + 's'
                });
            });
        }
        
        // Update visualizer every 200ms for dynamic effect
        setInterval(animateAudioBars, 200);
    }

    // Time Updates
    function initTimeUpdates() {
        updateCurrentTime();
        setInterval(updateCurrentTime, 60000); // Update every minute
        
        updateShowStatus();
        setInterval(updateShowStatus, 300000); // Update every 5 minutes
    }

    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit' 
        });
        
        // Update any time displays
        $('.current-time').text(timeString);
    }

    function updateShowStatus() {
        // In a real implementation, this would check against actual show times
        $('.show-card').each(function() {
            const $card = $(this);
            const timeRange = $card.find('.time-display').text();
            
            // Parse time range and compare with current time
            // Update status badges accordingly
            updateCardStatus($card, timeRange);
        });
    }

    function updateCardStatus($card, timeRange) {
        const now = new Date();
        const currentHour = now.getHours();
        
        // Simplified status update logic
        // In production, this would use actual show scheduling data
        if (currentHour >= 6 && currentHour < 8) {
            $card.removeClass('upcoming scheduled').addClass('live');
            $card.find('.status-badge').removeClass('upcoming').addClass('live').html('<div class="status-dot"></div>LIVE');
        } else if (currentHour >= 8 && currentHour < 10) {
            $card.removeClass('live scheduled').addClass('upcoming');
        }
    }

    // Scroll Animations
    function initScrollAnimations() {
        // Animate cards on scroll
        $(window).on('scroll', function() {
            animateVisibleCards();
        });
        
        // Initial animation
        setTimeout(() => {
            animateVisibleCards();
        }, 500);
    }

    function animateVisibleCards() {
        $('.show-card, .now-playing-card').each(function() {
            const $card = $(this);
            const cardTop = $card.offset().top;
            const cardBottom = cardTop + $card.outerHeight();
            const windowTop = $(window).scrollTop();
            const windowBottom = windowTop + $(window).height();
            
            // Check if card is in viewport
            if (cardBottom > windowTop && cardTop < windowBottom) {
                if (!$card.hasClass('animated')) {
                    $card.addClass('animated slide-in-up');
                }
            }
        });
    }

    // Notification System for Show Reminders
    function initNotificationSystem() {
        $('.action-btn').on('click', function(e) {
            const $btn = $(this);
            
            if ($btn.hasClass('secondary')) { // "SET REMINDERS" button
                e.preventDefault();
                handleReminderRequest();
            }
        });
    }

    function handleReminderRequest() {
        // Check for notification permission
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                showReminderOptions();
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        showReminderOptions();
                    }
                });
            }
        } else {
            // Fallback for browsers without notification support
            showReminderModal();
        }
    }

    function showReminderOptions() {
        // Create reminder selection modal
        const modalHTML = `
            <div class="schedule-modal-overlay">
                <div class="schedule-modal">
                    <div class="modal-header">
                        <h3>Set Show Reminders</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-content">
                        <p>Choose shows you'd like to be reminded about:</p>
                        <div class="reminder-options">
                            <label><input type="checkbox" value="morning-glory"> Morning Glory</label>
                            <label><input type="checkbox" value="gospel-vibration"> Gospel Vibration</label>
                            <label><input type="checkbox" value="prayer-praise"> Prayer & Praise</label>
                            <label><input type="checkbox" value="youth-connect"> Youth Connect</label>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary set-reminders">Set Reminders</button>
                            <button class="btn-secondary modal-close">Cancel</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(modalHTML);
        $('.schedule-modal-overlay').fadeIn(300);
        
        // Handle modal interactions
        $('.modal-close').on('click', closeScheduleModal);
        $('.set-reminders').on('click', activateReminders);
        $('.schedule-modal-overlay').on('click', function(e) {
            if (e.target === this) {
                closeScheduleModal();
            }
        });
    }

    function activateReminders() {
        const selectedShows = $('.reminder-options input:checked').map(function() {
            return $(this).val();
        }).get();
        
        // Store reminders in localStorage
        localStorage.setItem('elshadaifm_reminders', JSON.stringify(selectedShows));
        
        // Show confirmation
        showNotification('Reminders set successfully! You\'ll be notified before selected shows.', 'success');
        
        closeScheduleModal();
    }

    function closeScheduleModal() {
        $('.schedule-modal-overlay').fadeOut(300, function() {
            $(this).remove();
        });
    }

    // Show Schedule Modal for detailed show information
    function showScheduleModal(title, time, $card) {
        const description = $card.find('.show-description').text();
        const host = $card.find('.show-host').text();
        
        const modalHTML = `
            <div class="schedule-modal-overlay">
                <div class="schedule-modal show-details">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-content">
                        <div class="show-detail-info">
                            <p class="show-time-detail">${time}</p>
                            <p class="show-host-detail">${host}</p>
                            <p class="show-description-detail">${description}</p>
                        </div>
                        <div class="modal-actions">
                            <button class="btn-primary listen-now">Listen Now</button>
                            <button class="btn-secondary set-reminder">Set Reminder</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(modalHTML);
        $('.schedule-modal-overlay').fadeIn(300);
        
        // Handle modal interactions
        $('.modal-close').on('click', closeScheduleModal);
        $('.listen-now').on('click', function() {
            // In production, this would start the audio stream
            showNotification('Connecting to live stream...', 'info');
            closeScheduleModal();
        });
        $('.set-reminder').on('click', function() {
            // Set individual reminder
            showNotification(`Reminder set for ${title}`, 'success');
            closeScheduleModal();
        });
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notificationClass = `notification ${type}`;
        const notificationHTML = `
            <div class="${notificationClass}">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;
        
        $('body').append(notificationHTML);
        
        const $notification = $('.notification').last();
        $notification.slideDown(300);
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            $notification.slideUp(300, function() {
                $(this).remove();
            });
        }, 5000);
        
        // Handle close button
        $notification.find('.notification-close').on('click', function() {
            $notification.slideUp(300, function() {
                $(this).remove();
            });
        });
    }

    // Enhanced CSS animations (added via JavaScript)
    function addEnhancedAnimations() {
        const style = $(`
            <style>
                .card-ripple {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    width: 0;
                    height: 0;
                    border-radius: 50%;
                    background: rgba(255, 0, 64, 0.3);
                    transform: translate(-50%, -50%);
                    animation: rippleEffect 0.6s ease-out;
                }
                
                @keyframes rippleEffect {
                    to {
                        width: 200px;
                        height: 200px;
                        opacity: 0;
                    }
                }
                
                .enhanced-glow {
                    box-shadow: 0 0 30px rgba(255, 0, 64, 0.4) !important;
                }
                
                .card-clicked {
                    transform: scale(0.98) !important;
                }
                
                .slide-in-up {
                    animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
                }
                
                @keyframes slideInUp {
                    from {
                        opacity: 0;
                        transform: translateY(50px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }
                
                .nav-btn.clicked {
                    transform: scale(0.95) translateY(-2px);
                }
                
                .schedule-modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    z-index: 9999;
                    display: none;
                }
                
                .schedule-modal {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: linear-gradient(135deg, #1a1a1a, #0f0f0f);
                    border: 2px solid rgba(255, 0, 64, 0.3);
                    border-radius: 15px;
                    padding: 30px;
                    min-width: 400px;
                    max-width: 90vw;
                    color: white;
                }
                
                .modal-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                    padding-bottom: 15px;
                }
                
                .modal-header h3 {
                    margin: 0;
                    color: #ff0040;
                    font-size: 1.5rem;
                }
                
                .modal-close {
                    background: none;
                    border: none;
                    color: #cccccc;
                    font-size: 1.5rem;
                    cursor: pointer;
                    padding: 5px;
                    line-height: 1;
                }
                
                .modal-close:hover {
                    color: #ff0040;
                }
                
                .reminder-options {
                    margin: 20px 0;
                }
                
                .reminder-options label {
                    display: block;
                    margin: 10px 0;
                    cursor: pointer;
                }
                
                .reminder-options input {
                    margin-right: 10px;
                }
                
                .modal-actions {
                    display: flex;
                    gap: 15px;
                    justify-content: flex-end;
                    margin-top: 25px;
                }
                
                .btn-primary, .btn-secondary {
                    padding: 10px 20px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-weight: 600;
                    transition: all 0.3s ease;
                }
                
                .btn-primary {
                    background: linear-gradient(135deg, #ff0040, #ff3366);
                    color: white;
                }
                
                .btn-primary:hover {
                    background: linear-gradient(135deg, #ff3366, #ff0040);
                    transform: translateY(-2px);
                }
                
                .btn-secondary {
                    background: transparent;
                    color: #cccccc;
                    border: 1px solid rgba(255, 255, 255, 0.3);
                }
                
                .btn-secondary:hover {
                    background: rgba(255, 255, 255, 0.1);
                    color: white;
                }
                
                .notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 8px;
                    color: white;
                    z-index: 10000;
                    min-width: 300px;
                    display: none;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
                }
                
                .notification.success {
                    background: linear-gradient(135deg, #00ff7f, #32cd32);
                }
                
                .notification.info {
                    background: linear-gradient(135deg, #00bfff, #1e90ff);
                }
                
                .notification.error {
                    background: linear-gradient(135deg, #ff4444, #cc0000);
                }
                
                .notification-close {
                    float: right;
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    font-size: 1.2rem;
                    margin-left: 10px;
                }
            </style>
        `);
        
        $('head').append(style);
    }

    // Initialize enhanced animations
    addEnhancedAnimations();

    // Handle page visibility for audio visualizer performance
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // Pause intensive animations when page is not visible
            $('.audio-visualizer .bar').css('animation-play-state', 'paused');
        } else {
            $('.audio-visualizer .bar').css('animation-play-state', 'running');
        }
    });

    // ===================================================================
    // INTEGRATED TESTIMONIALS FUNCTIONALITY
    // ===================================================================
    
    class IntegratedTestimonialsRotator {
        constructor() {
            this.currentIndex = 0;
            this.totalTestimonials = 0;
            this.autoRotateInterval = null;
            this.isTransitioning = false;
            this.autoRotateDelay = 5000; // 5 seconds
            this.transitionDuration = 800; // 0.8 seconds
            
            this.init();
        }
        
        init() {
            this.cacheElements();
            
            if (this.totalTestimonials > 0) {
                this.setupEventListeners();
                this.startAutoRotation();
                this.setupHoverPause();
                this.handleVisibilityChange();
            }
        }
        
        cacheElements() {
            this.$section = $('.testimonials-within-broadcast');
            this.$testimonials = $('.testimonial-item-broadcast');
            this.$indicators = $('.indicator-broadcast');
            this.$container = $('.testimonial-container-broadcast');
            
            this.totalTestimonials = this.$testimonials.length;
            
            // Ensure first testimonial is active
            if (this.totalTestimonials > 0) {
                this.$testimonials.removeClass('active');
                this.$indicators.removeClass('active');
                this.$testimonials.eq(0).addClass('active');
                this.$indicators.eq(0).addClass('active');
            }
        }
        
        setupEventListeners() {
            // Indicator click events
            this.$indicators.on('click', (e) => {
                const targetIndex = parseInt($(e.currentTarget).data('testimonial'));
                this.goToTestimonial(targetIndex);
            });
            
            // Touch/swipe support for mobile
            this.setupTouchEvents();
        }
        
        setupTouchEvents() {
            let startX = 0;
            let endX = 0;
            const threshold = 50; // Minimum swipe distance
            
            this.$container.on('touchstart', (e) => {
                startX = e.originalEvent.touches[0].clientX;
            });
            
            this.$container.on('touchend', (e) => {
                endX = e.originalEvent.changedTouches[0].clientX;
                const diff = startX - endX;
                
                if (Math.abs(diff) > threshold) {
                    if (diff > 0) {
                        // Swipe left - next testimonial
                        this.nextTestimonial();
                    } else {
                        // Swipe right - previous testimonial
                        this.previousTestimonial();
                    }
                }
            });
        }
        
        setupHoverPause() {
            this.$section.on('mouseenter', () => {
                this.pauseAutoRotation();
            });
            
            this.$section.on('mouseleave', () => {
                this.resumeAutoRotation();
            });
        }
        
        handleVisibilityChange() {
            $(document).on('visibilitychange', () => {
                if (document.hidden) {
                    this.pauseAutoRotation();
                } else {
                    this.resumeAutoRotation();
                }
            });
        }
        
        goToTestimonial(index, skipAutoRestart = false) {
            if (this.isTransitioning || index === this.currentIndex || index >= this.totalTestimonials) {
                return;
            }
            
            this.isTransitioning = true;
            
            // Pause auto-rotation during transition
            if (!skipAutoRestart) {
                this.pauseAutoRotation();
            }
            
            // Update indicators
            this.$indicators.removeClass('active');
            this.$indicators.eq(index).addClass('active');
            
            // Fade out current testimonial
            const $currentTestimonial = this.$testimonials.eq(this.currentIndex);
            const $nextTestimonial = this.$testimonials.eq(index);
            
            $currentTestimonial.removeClass('active');
            
            // Wait for fade out, then fade in new testimonial
            setTimeout(() => {
                $nextTestimonial.addClass('active');
                this.currentIndex = index;
                
                // Re-enable transitions after animation completes
                setTimeout(() => {
                    this.isTransitioning = false;
                    
                    // Resume auto-rotation if not manually triggered
                    if (!skipAutoRestart) {
                        this.resumeAutoRotation();
                    }
                }, this.transitionDuration);
                
            }, this.transitionDuration / 2);
        }
        
        nextTestimonial() {
            const nextIndex = (this.currentIndex + 1) % this.totalTestimonials;
            this.goToTestimonial(nextIndex);
        }
        
        previousTestimonial() {
            const prevIndex = this.currentIndex === 0 ? this.totalTestimonials - 1 : this.currentIndex - 1;
            this.goToTestimonial(prevIndex);
        }
        
        startAutoRotation() {
            if (this.totalTestimonials <= 1) return;
            
            this.autoRotateInterval = setInterval(() => {
                if (!this.isTransitioning && document.visibilityState === 'visible') {
                    const nextIndex = (this.currentIndex + 1) % this.totalTestimonials;
                    this.goToTestimonial(nextIndex, true); // Skip auto-restart to maintain interval
                }
            }, this.autoRotateDelay);
        }
        
        pauseAutoRotation() {
            if (this.autoRotateInterval) {
                clearInterval(this.autoRotateInterval);
                this.autoRotateInterval = null;
            }
        }
        
        resumeAutoRotation() {
            if (!this.autoRotateInterval && this.totalTestimonials > 1) {
                this.startAutoRotation();
            }
        }
    }
    
    // Initialize integrated testimonials when section is visible
    function initIntegratedTestimonials() {
        const $testimonialsSection = $('.testimonials-within-broadcast');
        
        if ($testimonialsSection.length === 0) return;
        
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        // Initialize testimonials rotator when section comes into view
                        window.integratedTestimonialsRotator = new IntegratedTestimonialsRotator();
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.2, // Trigger when 20% of the section is visible
                rootMargin: '50px 0px' // Start loading 50px before section is visible
            });
            
            observer.observe($testimonialsSection[0]);
        } else {
            // Fallback for browsers without Intersection Observer
            window.integratedTestimonialsRotator = new IntegratedTestimonialsRotator();
        }
    }
    
    // Initialize integrated testimonials
    initIntegratedTestimonials();
    
    // Expose global controls for external usage
    window.IntegratedTestimonialsController = {
        pause: function() {
            if (window.integratedTestimonialsRotator) {
                window.integratedTestimonialsRotator.pauseAutoRotation();
            }
        },
        resume: function() {
            if (window.integratedTestimonialsRotator) {
                window.integratedTestimonialsRotator.resumeAutoRotation();
            }
        },
        next: function() {
            if (window.integratedTestimonialsRotator) {
                window.integratedTestimonialsRotator.nextTestimonial();
            }
        },
        previous: function() {
            if (window.integratedTestimonialsRotator) {
                window.integratedTestimonialsRotator.previousTestimonial();
            }
        }
    };
    
});