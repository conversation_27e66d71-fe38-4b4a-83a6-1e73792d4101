<?php
/**
 * AJAX Handlers for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class Efrai_AJAX_Handlers {

    private $main_plugin;

    public function __construct($main_plugin) {
        $this->main_plugin = $main_plugin;
    }

    /**
     * Register all AJAX handlers
     */
    public function register_ajax_handlers() {
        // Test connections
        add_action('wp_ajax_efrai_test_openai_connection', array($this, 'test_openai_connection'));
        add_action('wp_ajax_efrai_test_google_connection', array($this, 'test_google_connection'));

        // Content generation
        add_action('wp_ajax_efrai_generate_content', array($this, 'generate_content'));
        add_action('wp_ajax_efrai_generate_image', array($this, 'generate_image'));

        // Renungan management
        add_action('wp_ajax_efrai_save_renungan_post', array($this, 'save_renungan_post'));
        add_action('wp_ajax_efrai_check_existing_renungan', array($this, 'check_existing_renungan'));
        add_action('wp_ajax_efrai_extract_and_publish', array($this, 'extract_and_publish'));

        // New multi-date workflow
        add_action('wp_ajax_efrai_extract_dates', array($this, 'extract_dates'));
        add_action('wp_ajax_efrai_generate_for_date', array($this, 'generate_for_date'));

        // Legacy handlers
        add_action('wp_ajax_efrai_parse_input_file', array($this, 'parse_input_file'));
    }

    /**
     * Test OpenAI connection
     */
    public function test_openai_connection() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        try {
            $result = $this->main_plugin->test_openai_connection();
            wp_send_json_success($result);
        } catch (Exception $e) {
            wp_send_json_error('Connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Test Google AI connection
     */
    public function test_google_connection() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        try {
            $result = $this->main_plugin->test_google_connection();
            wp_send_json_success($result);
        } catch (Exception $e) {
            wp_send_json_error('Connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate content using OpenAI
     */
    public function generate_content() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        $tanggal = sanitize_text_field($_POST['tanggal']);
        $input_content = sanitize_textarea_field($_POST['content']);

        if (empty($tanggal) || empty($input_content)) {
            wp_send_json_error('Date and content are required');
        }

        try {
            $result = $this->main_plugin->generate_renungan_from_openai($tanggal, $input_content);
            wp_send_json_success($result);
        } catch (Exception $e) {
            wp_send_json_error('Failed to generate content: ' . $e->getMessage());
        }
    }

    /**
     * Generate image using Google AI
     */
    public function generate_image() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        $prompt = sanitize_textarea_field($_POST['prompt']);
        $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;

        if (empty($prompt)) {
            wp_send_json_error('Prompt is required');
        }

        try {
            $image_data = $this->main_plugin->generate_image_from_google_ai($prompt);

            if ($post_id) {
                $attachment_id = $this->main_plugin->attach_base64_image_to_post($image_data, $post_id);
                if ($attachment_id) {
                    set_post_thumbnail($post_id, $attachment_id);
                    update_post_meta($post_id, '_efrai_generated_image', true);
                    update_post_meta($post_id, '_efrai_image_prompt', $prompt);
                }
            }

            wp_send_json_success(array(
                'image_url' => 'data:image/jpeg;base64,' . $image_data,
                'attachment_id' => $attachment_id ?? null
            ));
        } catch (Exception $e) {
            wp_send_json_error('Failed to generate image: ' . $e->getMessage());
        }
    }

    /**
     * Save renungan post
     */
    public function save_renungan_post() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        $title = sanitize_text_field($_POST['title']);
        $content = wp_kses_post($_POST['content']);
        $tanggal = sanitize_text_field($_POST['tanggal']);
        $ayat = sanitize_text_field($_POST['ayat']);
        $tema = sanitize_text_field($_POST['tema']);

        if (empty($title) || empty($content)) {
            wp_send_json_error('Title and content are required');
        }

        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'draft',
            'post_type' => 'renungan',
            'post_date' => $tanggal . ' 07:00:00'
        );

        $post_id = wp_insert_post($post_data, true);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Failed to create post: ' . $post_id->get_error_message());
        }

        // Save meta data
        update_post_meta($post_id, '_efrai_tanggal', $tanggal);
        update_post_meta($post_id, '_efrai_ayat', $ayat);
        update_post_meta($post_id, '_efrai_tema', $tema);

        wp_send_json_success(array(
            'post_id' => $post_id,
            'edit_url' => get_edit_post_link($post_id)
        ));
    }

    /**
     * Check existing renungan for date
     */
    public function check_existing_renungan() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        $content = sanitize_textarea_field($_POST['content']);
        $extracted_date = $this->main_plugin->extract_date_from_content($content);

        if (!$extracted_date) {
            wp_send_json_error('Could not extract date from content');
        }

        $existing_posts = $this->main_plugin->get_renungan_by_date($extracted_date);

        if (!empty($existing_posts)) {
            wp_send_json_success(array(
                'exists' => true,
                'date' => $extracted_date,
                'post_title' => $existing_posts[0]->post_title,
                'post_status' => $existing_posts[0]->post_status,
                'edit_url' => get_edit_post_link($existing_posts[0]->ID),
                'view_url' => get_permalink($existing_posts[0]->ID)
            ));
        }

        wp_send_json_success(array(
            'exists' => false,
            'date' => $extracted_date
        ));
    }

    /**
     * Extract and publish (legacy single date workflow)
     */
    public function extract_and_publish() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        $content = sanitize_textarea_field($_POST['content']);
        $extracted_date = $this->main_plugin->extract_date_from_content($content);

        if (!$extracted_date) {
            wp_send_json_error('Could not extract date from content');
        }

        // Check for existing renungan first
        $existing_posts = $this->main_plugin->get_renungan_by_date($extracted_date);

        if (!empty($existing_posts)) {
            wp_send_json_error('Renungan for this date already exists: ' . $existing_posts[0]->post_title);
        }

        // Generate renungan content
        $generated_result = $this->main_plugin->generate_renungan_from_openai($content, $extracted_date);

        // Create post
        $post_data = array(
            'post_title' => $generated_result['judul'],
            'post_content' => $generated_result['konten'],
            'post_status' => 'publish',
            'post_type' => 'renungan',
            'post_date' => $extracted_date . ' 07:00:00'
        );

        $post_id = wp_insert_post($post_data, true);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Failed to create post: ' . $post_id->get_error_message());
        }

        // Save meta data
        update_post_meta($post_id, '_efrai_tanggal', $extracted_date);
        update_post_meta($post_id, '_efrai_ayat', $generated_result['ayat']);
        update_post_meta($post_id, '_efrai_tema', $generated_result['tema']);

        // Generate and attach image
        try {
            $image_prompt = "Christian devotional image about \"{$generated_result['judul']}\" with theme: {$generated_result['tema']}. Beautiful, inspiring, spiritual, modern digital art style.";
            $image_data = $this->main_plugin->generate_image_from_google_ai($image_prompt);
            $attachment_id = $this->main_plugin->attach_base64_image_to_post($image_data, $post_id);

            if ($attachment_id) {
                set_post_thumbnail($post_id, $attachment_id);
                update_post_meta($post_id, '_efrai_generated_image', true);
                update_post_meta($post_id, '_efrai_image_prompt', $image_prompt);
            }
        } catch (Exception $e) {
            error_log('Image generation failed: ' . $e->getMessage());
        }

        wp_send_json_success(array(
            'post_id' => $post_id,
            'post_title' => $generated_result['judul'],
            'post_url' => get_permalink($post_id),
            'edit_url' => get_edit_post_link($post_id)
        ));
    }

    /**
     * Extract dates using AI and generate content
     */
    public function extract_dates() {
        try {
            check_ajax_referer('efrai_admin_nonce', 'nonce');

            if (!current_user_can('edit_posts')) {
                wp_send_json_error('Permission denied');
            }

            if (!isset($_POST['content'])) {
                wp_send_json_error('Content parameter is missing');
            }

            $content = sanitize_textarea_field($_POST['content']);

            if (empty($content)) {
                wp_send_json_error('Content is required');
            }

            // Check if method exists
            if (!method_exists($this->main_plugin->get_openai(), 'extract_dates_and_generate_content')) {
                wp_send_json_error('Method not available: extract_dates_and_generate_content');
            }

            // Use AI to extract dates and generate content
            $ai_results = $this->main_plugin->get_openai()->extract_dates_and_generate_content($content);

            if (empty($ai_results)) {
                wp_send_json_success(array());
            }

            $result = array();
            foreach ($ai_results as $item) {
                // Validate AI response data
                if (!isset($item['date']) || !isset($item['title'])) {
                    continue; // Skip invalid entries
                }

                // Check for existing renungan
                try {
                    $existing_posts = $this->main_plugin->get_renungan_by_date($item['date']);
                } catch (Exception $e) {
                    error_log('EfraI: Error checking existing posts: ' . $e->getMessage());
                    $existing_posts = array();
                }

                // Debug log
                error_log('EfraI: Processing AI item: ' . print_r($item, true));

                $result[] = array(
                    'date' => isset($item['date']) ? $item['date'] : 'unknown',
                    'formatted_date' => isset($item['formatted_date']) ? $item['formatted_date'] : (isset($item['date']) ? $item['date'] : 'unknown'),
                    'title' => isset($item['title']) ? $item['title'] : 'Untitled Renungan',
                    'verses' => isset($item['verses']) ? $item['verses'] : (isset($item['verse']) ? [['ayat' => $item['verse'], 'isi' => '']] : []),
                    'theme' => isset($item['theme']) ? $item['theme'] : '',
                    'content' => isset($item['content']) ? $item['content'] : 'No content available',
                    'exists' => !empty($existing_posts),
                    'existing_post' => !empty($existing_posts) ? array(
                        'id' => $existing_posts[0]->ID,
                        'title' => $existing_posts[0]->post_title,
                        'status' => $existing_posts[0]->post_status,
                        'edit_url' => get_edit_post_link($existing_posts[0]->ID),
                        'view_url' => get_permalink($existing_posts[0]->ID)
                    ) : null
                );
            }

            wp_send_json_success($result);

        } catch (Exception $e) {
            error_log('EfraI: AI extract dates error: ' . $e->getMessage());
            wp_send_json_error('Server error: ' . $e->getMessage());
        }
    }

    /**
     * Publish pre-generated renungan for specific date
     */
    public function generate_for_date() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        $date = sanitize_text_field($_POST['date']);
        $title = sanitize_text_field($_POST['title']);
        $verses = isset($_POST['verses']) ? json_decode(stripslashes($_POST['verses']), true) : [];
        $verse = isset($_POST['verse']) ? sanitize_text_field($_POST['verse']) : '';
        $theme = isset($_POST['theme']) ? sanitize_text_field($_POST['theme']) : '';
        $content = isset($_POST['content']) ? wp_kses_post($_POST['content']) : '';

        if (empty($date) || empty($title) || empty($content)) {
            wp_send_json_error('Date, title, and content are required');
        }

        // Check for existing renungan first
        $existing_posts = $this->main_plugin->get_renungan_by_date($date);

        if (!empty($existing_posts)) {
            wp_send_json_error('Renungan for this date already exists');
        }

        // Create post with pre-generated content
        $post_data = array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'publish',
            'post_type' => 'renungan',
            'post_date' => $date . ' 07:00:00'
        );

        $post_id = wp_insert_post($post_data, true);

        if (is_wp_error($post_id)) {
            wp_send_json_error('Failed to create post: ' . $post_id->get_error_message());
        }

        // Save meta data
        update_post_meta($post_id, '_efrai_tanggal', $date);
        update_post_meta($post_id, '_efrai_ayat', $verse);
        update_post_meta($post_id, '_efrai_tema', $theme);

        // Save verses array if available
        if (!empty($verses) && is_array($verses)) {
            update_post_meta($post_id, '_efrai_verses', $verses);
        }

        // Generate and attach image
        try {
            $image_prompt = "Christian devotional image about \"{$title}\" with theme: {$theme}. Beautiful, inspiring, spiritual, modern digital art style.";
            $image_data = $this->main_plugin->generate_image_from_google_ai($image_prompt);
            $attachment_id = $this->main_plugin->attach_base64_image_to_post($image_data, $post_id);

            if ($attachment_id) {
                set_post_thumbnail($post_id, $attachment_id);
                update_post_meta($post_id, '_efrai_generated_image', true);
                update_post_meta($post_id, '_efrai_image_prompt', $image_prompt);
            }
        } catch (Exception $e) {
            error_log('Image generation failed: ' . $e->getMessage());
        }

        wp_send_json_success(array(
            'post_id' => $post_id,
            'post_title' => $title,
            'post_url' => get_permalink($post_id),
            'edit_url' => get_edit_post_link($post_id)
        ));
    }

    /**
     * Parse input file (legacy)
     */
    public function parse_input_file() {
        check_ajax_referer('efrai_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Permission denied');
        }

        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error('File upload failed');
        }

        $file_content = file_get_contents($_FILES['file']['tmp_name']);

        if (!$file_content) {
            wp_send_json_error('Could not read file content');
        }

        $extracted_date = $this->main_plugin->extract_date_from_content($file_content);

        if (!$extracted_date) {
            wp_send_json_error('Could not extract date from file');
        }

        wp_send_json_success(array(
            'date' => $extracted_date,
            'content' => $file_content
        ));
    }
}