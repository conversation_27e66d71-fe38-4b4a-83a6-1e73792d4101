<?php
/**
 * Helper Module for Elshadaifm Renungan API
 * Utility functions and helpers
 */

class Elshadaifm_Renungan_Helper {

    /**
     * Generate SHA256 hash
     */
    public static function generate_hash($content) {
        return hash('sha256', $content);
    }

    /**
     * Format API response
     */
    public static function format_response($success, $message, $data = array()) {
        $response = array(
            'success' => $success,
            'message' => $message
        );

        if (!empty($data)) {
            $response = array_merge($response, $data);
        }

        return $response;
    }

    /**
     * Format success response
     */
    public static function success_response($message, $data = array()) {
        return self::format_response(true, $message, $data);
    }

    /**
     * Format error response
     */
    public static function error_response($message, $status_code = 500) {
        return new WP_Error(
            'api_error',
            $message,
            array('status' => $status_code)
        );
    }

    /**
     * Clean markdown content
     */
    public static function clean_markdown($content, $format = 'json') {
        if ($format === 'json') {
            // Remove markdown code block wrapper for JSON
            $content = preg_replace('/^```json\n?/m', '', $content);
            $content = preg_replace('/\n?```$/m', '', $content);
        } else {
            // Remove generic markdown code blocks
            $content = preg_replace('/^```[a-z]*\n?/m', '', $content);
            $content = preg_replace('/\n?```$/m', '', $content);
        }

        return trim($content);
    }

    /**
     * Parse JSON response safely
     */
    public static function parse_json_response($content) {
        // Check if content is already an array (OpenAI response)
        if (is_array($content)) {
            // Extract the actual content from OpenAI response structure
            if (isset($content['choices'][0]['message']['content'])) {
                $content = $content['choices'][0]['message']['content'];
            } else {
                return false;
            }
        }

        // Clean markdown from the content string
        $content = self::clean_markdown($content, 'json');
        $data = json_decode($content, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        return $data;
    }

    /**
     * Generate unique filename
     */
    public static function generate_filename($prefix = 'renungan_', $extension = 'jpg') {
        return $prefix . time() . '.' . $extension;
    }

    /**
     * Create upload directory if not exists
     */
    public static function ensure_upload_directory() {
        $upload_dir = wp_upload_dir();

        if (!file_exists($upload_dir['path'])) {
            wp_mkdir_p($upload_dir['path']);
        }

        return $upload_dir;
    }

    /**
     * Load style guide file
     */
    public static function load_style_guide() {
        $style_guide_path = plugin_dir_path(dirname(__DIR__)) . 'lang_style.json';

        if (file_exists($style_guide_path)) {
            return file_get_contents($style_guide_path);
        }

        // Return default style guide if file not found
        return json_encode(array(
            'tone' => 'Otoritatif namun Peduli',
            'structure' => 'Pembukaan → Pernyataan Inti → Isi → Penutup',
            'language' => 'Formal namun hangat dengan pendekatan personal'
        ));
    }

    /**
     * Truncate text with ellipsis
     */
    public static function truncate_text($text, $length = 100, $ellipsis = '...') {
        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . $ellipsis;
    }

    /**
     * Format date for display
     */
    public static function format_date($date, $format = 'd M Y') {
        if (empty($date)) {
            return '';
        }

        $datetime = DateTime::createFromFormat('Y-m-d', $date);
        if (!$datetime) {
            return $date;
        }

        return $datetime->format($format);
    }

    /**
     * Sanitize array recursively
     */
    public static function sanitize_array($array) {
        if (!is_array($array)) {
            return sanitize_text_field($array);
        }

        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $array[$key] = self::sanitize_array($value);
            } else {
                $array[$key] = sanitize_text_field($value);
            }
        }

        return $array;
    }

    /**
     * Get client IP address
     */
    public static function get_client_ip() {
        $ip_keys = array(
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                        return $ip;
                    }
                }
            }
        }

        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : 'Unknown';
    }

    /**
     * Measure execution time
     */
    public static function measure_time($callback) {
        $start_time = microtime(true);
        $result = $callback();
        $end_time = microtime(true);

        return array(
            'result' => $result,
            'execution_time' => $end_time - $start_time
        );
    }

    /**
     * Get memory usage
     */
    public static function get_memory_usage() {
        return memory_get_usage(true);
    }

    /**
     * Format file size
     */
    public static function format_file_size($bytes) {
        if ($bytes == 0) {
            return '0 Bytes';
        }

        $units = array('Bytes', 'KB', 'MB', 'GB', 'TB');
        $exponent = floor(log($bytes, 1024));

        return round($bytes / pow(1024, $exponent), 2) . ' ' . $units[$exponent];
    }

    /**
     * Check if string contains JSON
     */
    public static function is_json($string) {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * Safe array access
     */
    public static function array_get($array, $key, $default = null) {
        return isset($array[$key]) ? $array[$key] : $default;
    }

    /**
     * Convert seconds to human readable duration
     */
    public static function format_duration($seconds) {
        if ($seconds < 60) {
            return $seconds . ' seconds';
        }

        if ($seconds < 3600) {
            return floor($seconds / 60) . ' minutes';
        }

        return floor($seconds / 3600) . ' hours';
    }

    /**
     * Generate random string
     */
    public static function generate_random_string($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $string = '';

        for ($i = 0; $i < $length; $i++) {
            $string .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $string;
    }

    /**
     * Validate URL
     */
    public static function is_valid_url($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Extract domain from URL
     */
    public static function extract_domain($url) {
        $parsed_url = parse_url($url);
        return isset($parsed_url['host']) ? $parsed_url['host'] : '';
    }

    /**
     * Check if cURL is available
     */
    public static function is_curl_available() {
        return function_exists('curl_version');
    }

    /**
     * Get plugin version
     */
    public static function get_plugin_version() {
        $plugin_data = get_plugin_data(plugin_dir_path(dirname(__DIR__)) . 'elshadaifm-renungan-api.php');
        return $plugin_data['Version'];
    }

    /**
     * Get plugin directory URL
     */
    public static function get_plugin_url() {
        return plugin_dir_url(dirname(__DIR__));
    }

    /**
     * Get plugin directory path
     */
    public static function get_plugin_path() {
        return plugin_dir_path(dirname(__DIR__));
    }
}