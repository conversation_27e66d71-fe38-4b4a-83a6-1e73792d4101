<?php
/**
 * Configuration Module for Elshadaifm Renungan API
 * Handles plugin configuration and settings
 */

class Elshadaifm_Renungan_Config {

    /**
     * Plugin configuration constants
     */
    const API_PREFIX = 'elshadaifm_';
    const PLUGIN_VERSION = '1.0';
    const DB_VERSION = '1.0';

    /**
     * OpenAI API configuration
     */
    const OPENAI_CHAT_URL = 'https://api.openai.com/v1/chat/completions';
    const OPENAI_IMAGE_URL = 'https://api.openai.com/v1/images/generations';
    const OPENAI_MODEL = 'gpt-4o-mini';
    const OPENAI_IMAGE_MODEL = 'gpt-image-1';
    const OPENAI_TIMEOUT = 60;
    const OPENAI_CONNECT_TIMEOUT = 15;

    /**
     * Database configuration
     */
    const MAIN_TABLE = 'efr_renungan';
    const DETAIL_TABLE = 'efr_renungan_detail';

    /**
     * API endpoints configuration
     */
    const API_NAMESPACE = 'elshadaifm-renungan/v1';
    const API_ROUTES = array(
        'submit' => 'POST',
        'generate-details' => 'POST',
        'generate-content' => 'POST',
        'generate-image' => 'POST'
    );

    /**
     * Get option key
     */
    public static function get_option_key($key) {
        return self::API_PREFIX . $key;
    }

    /**
     * Get API key option
     */
    public static function get_api_key() {
        return get_option(self::get_option_key('api_key'));
    }

    /**
     * Set API key option
     */
    public static function set_api_key($api_key) {
        return update_option(self::get_option_key('api_key'), $api_key);
    }

    /**
     * Get OpenAI API key option
     */
    public static function get_openai_key() {
        return get_option(self::get_option_key('openai_key'));
    }

    /**
     * Set OpenAI API key option
     */
    public static function set_openai_key($openai_key) {
        return update_option(self::get_option_key('openai_key'), $openai_key);
    }

    /**
     * Get database version
     */
    public static function get_db_version() {
        return get_option(self::get_option_key('db_version'), '1.0');
    }

    /**
     * Set database version
     */
    public static function set_db_version($version) {
        return update_option(self::get_option_key('db_version'), $version);
    }

    /**
     * Get plugin settings
     */
    public static function get_settings() {
        return array(
            'api_key' => self::get_api_key(),
            'openai_key' => self::get_openai_key(),
            'db_version' => self::get_db_version(),
            'plugin_version' => self::PLUGIN_VERSION
        );
    }

    /**
     * Check if plugin is configured
     */
    public static function is_configured() {
        $api_key = self::get_api_key();
        $openai_key = self::get_openai_key();

        return !empty($api_key) && !empty($openai_key);
    }

    /**
     * Get default settings
     */
    public static function get_default_settings() {
        return array(
            'api_key' => '',
            'openai_key' => '',
            'db_version' => self::DB_VERSION,
            'plugin_version' => self::PLUGIN_VERSION
        );
    }

    /**
     * Reset settings to defaults
     */
    public static function reset_settings() {
        $defaults = self::get_default_settings();

        foreach ($defaults as $key => $value) {
            update_option(self::get_option_key($key), $value);
        }

        return true;
    }

    /**
     * Get table names
     */
    public static function get_table_names() {
        global $wpdb;

        return array(
            'main' => $wpdb->prefix . self::MAIN_TABLE,
            'detail' => $wpdb->prefix . self::DETAIL_TABLE
        );
    }

    /**
     * Get API endpoints
     */
    public static function get_api_endpoints() {
        $endpoints = array();

        foreach (self::API_ROUTES as $route => $method) {
            $endpoints[$route] = array(
                'url' => rest_url(self::API_NAMESPACE . '/' . $route),
                'method' => $method
            );
        }

        return $endpoints;
    }

    /**
     * Get OpenAI configuration
     */
    public static function get_openai_config() {
        return array(
            'chat_url' => self::OPENAI_CHAT_URL,
            'image_url' => self::OPENAI_IMAGE_URL,
            'model' => self::OPENAI_MODEL,
            'image_model' => self::OPENAI_IMAGE_MODEL,
            'timeout' => self::OPENAI_TIMEOUT,
            'connect_timeout' => self::OPENAI_CONNECT_TIMEOUT
        );
    }

    /**
     * Get content validation limits
     */
    public static function get_validation_limits() {
        return array(
            'content_min_length' => 10,
            'content_max_length' => 100000,
            'ayat_max_length' => 500,
            'judul_max_length' => 255,
            'summary_max_length' => 2000,
            'image_prompt_min_length' => 20,
            'image_prompt_max_length' => 4000,
            'generated_content_min_length' => 50,
            'generated_content_max_length' => 50000
        );
    }

    /**
     * Get image generation settings
     */
    public static function get_image_settings() {
        return array(
            'size' => '1024x1024',
            'format' => 'jpg',
            'quality' => 'standard'
        );
    }

    /**
     * Get cURL configuration
     */
    public static function get_curl_config() {
        return array(
            'timeout' => self::OPENAI_TIMEOUT,
            'connect_timeout' => self::OPENAI_CONNECT_TIMEOUT,
            'ssl_verifypeer' => false,
            'user_agent' => 'Elshadaifm-Renungan-API/' . self::PLUGIN_VERSION
        );
    }

    /**
     * Get debug settings
     */
    public static function get_debug_settings() {
        return array(
            'enable_debug' => WP_DEBUG,
            'enable_logging' => WP_DEBUG_LOG,
            'log_level' => WP_DEBUG ? 'debug' : 'error'
        );
    }

    /**
     * Get cache settings
     */
    public static function get_cache_settings() {
        return array(
            'enable_cache' => true,
            'cache_duration' => 3600, // 1 hour
            'cache_key_prefix' => self::API_PREFIX . 'cache_'
        );
    }

    /**
     * Get security settings
     */
    public static function get_security_settings() {
        return array(
            'require_authentication' => true,
            'rate_limit_enabled' => true,
            'rate_limit_requests' => 100,
            'rate_limit_window' => 3600 // 1 hour
        );
    }

    /**
     * Initialize default options
     */
    public static function initialize_options() {
        $defaults = self::get_default_settings();

        foreach ($defaults as $key => $value) {
            $option_key = self::get_option_key($key);

            if (get_option($option_key) === false) {
                update_option($option_key, $value);
            }
        }

        // Generate API key if not exists
        if (empty(self::get_api_key())) {
            self::set_api_key(self::generate_api_key());
        }
    }

    /**
     * Generate new API key
     */
    public static function generate_api_key() {
        return 'efm_' . bin2hex(random_bytes(16));
    }

    /**
     * Validate configuration
     */
    public static function validate_configuration() {
        $errors = array();

        if (empty(self::get_api_key())) {
            $errors[] = 'API key is not configured';
        }

        if (empty(self::get_openai_key())) {
            $errors[] = 'OpenAI API key is not configured';
        }

        if (!self::is_curl_available()) {
            $errors[] = 'cURL is not available';
        }

        return $errors;
    }

    /**
     * Check if cURL is available
     */
    private static function is_curl_available() {
        return function_exists('curl_version');
    }

    /**
     * Get plugin info
     */
    public static function get_plugin_info() {
        return array(
            'name' => 'Elshadaifm Renungan API',
            'version' => self::PLUGIN_VERSION,
            'db_version' => self::get_db_version(),
            'configured' => self::is_configured(),
            'settings' => self::get_settings()
        );
    }
}