/* ==========================================================================
   ABOUT US - RESPONSIVE STYLES
   ========================================================================== */

/* Tablet Styles (1024px and below) */
@media (max-width: 1024px) {
    .about-container {
        padding: 0 40px;
    }

    .vision-mission-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .cta-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .cta-action {
        text-align: center;
    }

    /* Timeline adjustments for tablet */
    .history-timeline {
        max-width: 800px;
    }

    .timeline-content {
        max-width: 350px;
        padding: 30px;
    }

    .timeline-item:nth-child(odd) .timeline-content {
        margin-right: 50px;
    }

    .timeline-item:nth-child(even) .timeline-content {
        margin-left: 50px;
    }

    .timeline-date {
        min-width: 120px;
        padding: 10px 20px;
        font-size: 0.85rem;
    }
}

/* Mobile Styles (768px and below) */
@media (max-width: 768px) {
    .about-intro, .history-section, .vision-mission-section, .values-section, .stats-section, .cta-section {
        padding: 60px 0;
    }

    .about-container {
        padding: 0 20px;
    }

    .about-title {
        font-size: 2rem;
    }

    .about-highlights {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Mobile Timeline Layout */
    .history-timeline {
        padding: 40px 0;
        max-width: 100%;
    }

    .history-timeline::before {
        left: 30px;
        width: 2px;
    }

    .timeline-item {
        flex-direction: column !important;
        align-items: flex-start;
        margin-bottom: 50px;
        padding-left: 80px;
        position: relative;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        margin: 0 !important;
        width: 100%;
        max-width: none;
    }

    .timeline-content::before {
        display: none;
    }

    .timeline-content {
        padding: 25px;
        border-radius: 16px;
    }

    .timeline-date {
        position: absolute;
        left: 30px;
        top: 20px;
        transform: translateX(-50%);
        min-width: auto;
        padding: 10px 18px;
        font-size: 0.85rem;
        white-space: nowrap;
    }

    .timeline-item::before {
        left: 30px;
        top: 25px;
        width: 12px;
        height: 12px;
        border-width: 3px;
    }

    .timeline-content h3 {
        font-size: 1.25rem;
        margin-bottom: 12px;
    }

    .timeline-content p {
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Mobile Timeline Specific Styles */
    .mobile-timeline {
        padding-left: 0;
    }

    .mobile-timeline .timeline-item {
        animation: slideInFromLeft 0.6s ease-out forwards;
        animation-fill-mode: both;
    }

    @keyframes slideInFromLeft {
        from {
            opacity: 0;
            transform: translateX(-30px) translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateX(0) translateY(0);
        }
    }

    /* Improved mobile date positioning */
    .mobile-timeline .timeline-date {
        font-size: 0.8rem;
        padding: 8px 14px;
        min-width: 100px;
    }

    .mobile-timeline .timeline-item::before {
        box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.15);
    }

    /* Additional mobile optimizations */
    .mobile-timeline .timeline-content h3 {
        font-size: 1.3rem;
    }

    .mobile-timeline .timeline-content p {
        font-size: 1rem;
        text-align: left;
    }

    /* Mobile expanded state */
    .timeline-content.expanded {
        transform: scale(1.02);
        box-shadow: 0 15px 40px rgba(139, 92, 246, 0.2);
        border-color: rgba(139, 92, 246, 0.3);
    }

    .timeline-content.expanded h3::after {
        width: 80px;
    }

    /* Add tap indicator for mobile */
    .timeline-item::after {
        content: '👆 Tap untuk detail';
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.75rem;
        color: #8B5CF6;
        opacity: 0.7;
        pointer-events: none;
    }

    .timeline-item.visible::after {
        animation: fadeInUp 0.5s ease 1s both;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(10px);
        }
        to {
            opacity: 0.7;
            transform: translateX(-50%) translateY(0);
        }
    }

    /* Hide progress indicator on mobile */
    .timeline-progress {
        display: none !important;
    }

    .values-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .vision-card, .mission-card {
        padding: 30px 20px;
    }

    .vision-card h3, .mission-card h3 {
        font-size: 1.5rem;
    }

    .cta-title {
        font-size: 2rem;
    }
}

/* Extra small devices (480px and below) */
@media (max-width: 480px) {
    .about-container {
        padding: 0 15px;
    }

    .history-timeline {
        padding: 30px 0;
    }

    .timeline-item {
        padding-left: 70px;
        margin-bottom: 40px;
    }

    .timeline-content {
        padding: 20px;
        border-radius: 12px;
    }

    .timeline-content h3 {
        font-size: 1.2rem;
        margin-bottom: 12px;
    }

    .timeline-content p {
        font-size: 0.9rem;
        line-height: 1.6;
    }

    .timeline-date {
        font-size: 0.75rem;
        padding: 6px 12px;
        min-width: 80px;
    }

    .timeline-item::before {
        width: 10px;
        height: 10px;
        border-width: 2px;
    }
}

/* Print styles */
@media print {
    .timeline-progress,
    .timeline-item::before,
    .timeline-item::after,
    .history-timeline::before {
        display: none !important;
    }

    .timeline-item {
        break-inside: avoid;
        margin-bottom: 30px;
        flex-direction: column !important;
    }

    .timeline-content {
        box-shadow: none !important;
        border: 1px solid #e2e8f0 !important;
        margin: 0 !important;
        max-width: none !important;
    }

    .timeline-date {
        position: static !important;
        transform: none !important;
        display: inline-block;
        margin-bottom: 10px;
        background: #8B5CF6 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .timeline-item,
    .timeline-content,
    .timeline-date,
    .timeline-item::before,
    .timeline-progress-fill {
        transition: none !important;
        animation: none !important;
    }

    .history-timeline::before {
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .timeline-content {
        border: 2px solid #000000;
        background: #ffffff;
    }

    .timeline-date {
        border: 2px solid #ffffff;
        background: #000000 !important;
        color: #ffffff !important;
    }

    .timeline-item::before {
        border-color: #000000;
        background: #ffffff;
    }

    .history-timeline::before {
        background: #000000;
    }
}

/* Fallback for older browsers */
@supports not (display: grid) {
    .timeline-item {
        display: block;
        margin-bottom: 40px;
    }

    .timeline-content {
        margin: 0 0 20px 0 !important;
        max-width: none !important;
    }

    .timeline-date {
        position: relative !important;
        transform: none !important;
        display: inline-block;
        margin-bottom: 15px;
    }
}
