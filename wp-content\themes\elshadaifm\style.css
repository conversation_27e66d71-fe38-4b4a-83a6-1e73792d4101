/*
Theme Name: ElshadaiFM Radio Station
Description: Modern WordPress theme untuk ElshadaiFM radio station dengan design mengikuti pro.radio.jpg - purple gradients, events, team, devotions, testimonials.
Author: Claude Code
Version: 2.0
License: GPL v2 or later
Text Domain: elshadaifm
*/

/* Import Navigation Styles */
@import url('css/navigation.css');

/* ==========================================================================
   CSS RESET AND BASE STYLES
   ========================================================================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.logo-image {
    max-width: 150px;
    height: auto;
}

a {
    color: #8B5CF6;
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #7C3AED;
}

/* ==========================================================================
   UTILITY CLASSES
   ========================================================================== */
.container {
    width: 90%;
    margin: 0 auto;
    padding: 0 20px;
}

.text-center {
    text-align: center;
}

.text-white {
    color: #ffffff !important;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }
.mb-4 { margin-bottom: 4rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }
.mt-4 { margin-top: 4rem; }

.py-1 { padding: 1rem 0; }
.py-2 { padding: 2rem 0; }
.py-3 { padding: 3rem 0; }
.py-4 { padding: 4rem 0; }

.btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    border: none;
    border-radius: 25px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn:hover {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
    color: white;
}

.btn-white {
    background: white;
    color: #8B5CF6;
}

.btn-white:hover {
    background: #f8fafc;
    color: #7C3AED;
}

/* ==========================================================================
   PURPLE GRADIENT STYLES
   ========================================================================== */
.gradient-bg {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED, #6D28D9);
}

.gradient-bg-light {
    background: linear-gradient(135deg, #C084FC, #A855F7);
}

.gradient-overlay {
    position: relative;
}

.gradient-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8));
}


/* ==========================================================================
   HOME PAGE SECTIONS
   ========================================================================== */

/* Next Event Section */
.next-event-section {
    padding: 60px 0;
    background: #f8fafc;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #1e293b;
    font-family: 'Poppins', sans-serif;
}

.section-title::after {
    content: '';
    display: block;
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    margin: 20px auto 0;
}

.event-card {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.event-speakers {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.speaker-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #8B5CF6;
}

/* Latest News Section */
.latest-news-section {
    padding: 80px 0;
    background: white;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.news-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.news-card:hover {
    transform: translateY(-10px);
}

.news-card-image {
    height: 200px;
    overflow: hidden;
}

.news-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-card-image img {
    transform: scale(1.1);
}

.news-card-content {
    padding: 25px;
}

.news-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #1e293b;
}

.news-card-meta {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 15px;
}

/* Team Section */
.team-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    position: relative;
}

.team-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
    position: relative;
    z-index: 2;
}

.team-member {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
}

.team-member-img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 auto 20px;
    border: 4px solid rgba(255, 255, 255, 0.3);
}

.team-member h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.team-member-role {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 15px;
}

/* Chart Section */
.chart-section {
    padding: 80px 0;
    background: #f8fafc;
}

.chart-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    margin-top: 50px;
}

.chart-entry {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.3s ease;
}

.chart-entry:hover {
    background-color: #f8fafc;
}

.chart-entry:last-child {
    border-bottom: none;
}

.chart-position {
    font-size: 2rem;
    font-weight: 700;
    color: #8B5CF6;
    margin-right: 20px;
    min-width: 60px;
}

.chart-song-info {
    flex: 1;
}

.chart-song-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.chart-artist {
    color: #64748b;
    font-size: 14px;
}

.chart-album-art {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    object-fit: cover;
    margin-left: 20px;
}

/* Top 3 Section */
.top3-section {
    padding: 80px 0;
    background: #1e293b;
    color: white;
}

.top3-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.top3-card {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    height: 300px;
}

.top3-card-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: cover;
    background-position: center;
}

.top3-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 30px;
}

.top3-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Featured Videos Section */
.videos-section {
    padding: 80px 0;
    background: white;
}

.video-player {
    background: #000;
    border-radius: 20px;
    overflow: hidden;
    margin: 30px 0;
    aspect-ratio: 16/9;
}

/* Prayer Section */
.prayer-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #C084FC, #A855F7);
    color: white;
}

.prayer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.prayer-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    backdrop-filter: blur(10px);
}

.prayer-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Newsletter Section */
.newsletter-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #1e293b, #334155);
    color: white;
    text-align: center;
}

.newsletter-form {
    max-width: 500px;
    margin: 30px auto 0;
    display: flex;
    gap: 15px;
}

.newsletter-input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
}

.newsletter-input:focus {
    outline: none;
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

/* ==========================================================================
   FOOTER STYLES
   ========================================================================== */
.site-footer {
    background: #0f172a;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    color: #8B5CF6;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul a {
    color: #cbd5e1;
    transition: color 0.3s ease;
}

.footer-section ul a:hover {
    color: #8B5CF6;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #8B5CF6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #7C3AED;
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid #334155;
    padding-top: 20px;
    text-align: center;
    color: #64748b;
}

/* ==========================================================================
   RESPONSIVE STYLES
   ========================================================================== */
@media (max-width: 768px) {
    .container {
        width: 95%;
        padding: 0 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .site-branding h1 {
        font-size: 2rem;
    }
    
    .main-navigation ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 20px;
    }
    
    .header-top-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .news-grid,
    .team-grid,
    .top3-grid,
    .prayer-grid {
        grid-template-columns: 1fr;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .event-speakers {
        flex-wrap: wrap;
    }
    
    .chart-entry {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .chart-position {
        margin: 0;
    }
    
    .chart-album-art {
        margin: 0;
    }
}

@media (max-width: 480px) {
    .container {
        width: 98%;
        padding: 0 10px;
    }

    .site-branding h1 {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 1.8rem;
    }
    
    .event-card,
    .chart-container {
        padding: 20px;
    }
    
    .team-member,
    .prayer-card {
        padding: 20px;
    }
    
}


/* ==========================================================================
   WORDPRESS SPECIFIC STYLES
   ========================================================================== */
.wp-block-group {
    margin-bottom: 2rem;
}

.wp-block-columns {
    display: flex;
    gap: 2rem;
}

.wp-block-column {
    flex: 1;
}

.aligncenter {
    text-align: center;
}

.alignleft {
    float: left;
    margin-right: 1rem;
}

.alignright {
    float: right;
    margin-left: 1rem;
}

.wp-caption {
    max-width: 100%;
}

.wp-caption-text {
    font-size: 14px;
    color: #64748b;
    text-align: center;
    margin-top: 10px;
}

/* Comments */
.comments-area {
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 1px solid #e2e8f0;
}

.comment-list {
    list-style: none;
    padding: 0;
}

.comment {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 10px;
}

.comment-author {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.comment-meta {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 1rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 3rem 0;
}

.pagination a,
.pagination span {
    padding: 10px 15px;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    color: #64748b;
    text-decoration: none;
}

.pagination a:hover {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.pagination .current {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

/* Screen reader text */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

/* ==========================================================================
   RENUNGAN STYLES
   ========================================================================== */

/* Single Renungan Page */
.renungan-single {
    background: #f8fafc;
    min-height: 100vh;
    padding: 40px 0;
}

.renungan-article {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.renungan-header {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 40px;
    text-align: center;
    position: relative;
}

.renungan-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid #7C3AED;
}

.renungan-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    font-size: 14px;
    opacity: 0.9;
}

.renungan-meta span {
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.renungan-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 20px 0;
    line-height: 1.2;
    font-family: 'Poppins', sans-serif;
}

.renungan-featured-image {
    margin-top: 30px;
    border-radius: 15px;
    overflow: hidden;
}

.renungan-featured-image img {
    width: 100%;
    height: auto;
    display: block;
}

.renungan-scripture {
    background: linear-gradient(135deg, #C084FC, #A855F7);
    margin: -20px 40px 40px;
    padding: 30px;
    border-radius: 15px;
    color: white;
    text-align: center;
    position: relative;
    z-index: 2;
}

.scripture-icon {
    font-size: 2rem;
    margin-bottom: 15px;
}

.scripture-verse {
    font-size: 1.3rem;
    font-style: italic;
    line-height: 1.6;
    margin: 0 0 15px 0;
    font-weight: 300;
}

.scripture-reference {
    font-size: 1rem;
    font-weight: 600;
    opacity: 0.9;
}

.renungan-content {
    padding: 40px;
    font-size: 1.1rem;
    line-height: 1.8;
    color: #334155;
}

.renungan-content p {
    margin-bottom: 20px;
}

.renungan-prayer {
    background: #f1f5f9;
    margin: 0 40px 40px;
    padding: 30px;
    border-radius: 15px;
    border-left: 4px solid #8B5CF6;
}

.prayer-title {
    color: #8B5CF6;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.prayer-title::before {
    content: '🙏';
    font-size: 1.2rem;
}

.prayer-content {
    color: #475569;
    font-style: italic;
    line-height: 1.7;
}

.renungan-author {
    text-align: center;
    padding: 20px 40px;
    border-top: 1px solid #e2e8f0;
    color: #64748b;
}

.author-label {
    font-size: 14px;
}

.author-name {
    font-weight: 600;
    color: #8B5CF6;
    margin-left: 5px;
}

.renungan-navigation {
    background: #f8fafc;
    padding: 30px 40px;
}

.nav-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.nav-link {
    display: block;
    padding: 20px;
    background: white;
    border-radius: 10px;
    text-decoration: none;
    color: #334155;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.2);
    color: #8B5CF6;
}

.nav-direction {
    display: block;
    font-size: 14px;
    color: #8B5CF6;
    font-weight: 600;
    margin-bottom: 5px;
}

.nav-title {
    display: block;
    font-weight: 500;
    line-height: 1.4;
}

.nav-next {
    text-align: right;
}

.renungan-share {
    padding: 30px 40px;
    background: #f8fafc;
    text-align: center;
}

.renungan-share h4 {
    color: #334155;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.share-buttons a {
    padding: 10px 20px;
    border-radius: 25px;
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.share-facebook {
    background: #1877f2;
}

.share-twitter {
    background: #1da1f2;
}

.share-whatsapp {
    background: #25d366;
}

.share-buttons a:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.related-renungan {
    max-width: 800px;
    margin: 40px auto 0;
    padding: 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.related-renungan h3 {
    color: #334155;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 30px;
    text-align: center;
}

.related-posts {
    display: grid;
    gap: 20px;
}

.related-post {
    padding: 20px;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.related-post:hover {
    border-color: #8B5CF6;
    background: #f8fafc;
}

.related-post h4 {
    margin-bottom: 10px;
}

.related-post h4 a {
    color: #334155;
    text-decoration: none;
    font-size: 1.1rem;
}

.related-post h4 a:hover {
    color: #8B5CF6;
}

.related-date {
    color: #64748b;
    font-size: 14px;
    margin-bottom: 10px;
}

.related-excerpt {
    color: #475569;
    line-height: 1.6;
}

/* Archive Renungan Page */
.renungan-archive {
    background: #f8fafc;
    min-height: 100vh;
    padding: 40px 0;
}

.archive-header {
    text-align: center;
    margin-bottom: 60px;
}

.archive-hero {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 60px 40px;
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.archive-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
}

.archive-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    position: relative;
    z-index: 2;
    font-family: 'Poppins', sans-serif;
}

.archive-description {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    line-height: 1.6;
}

.renungan-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 10px 20px;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    color: #64748b;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.search-box {
    display: flex;
    align-items: center;
}

.search-box form {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.search-box input[type="search"] {
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    outline: none;
    min-width: 250px;
}

.search-box button {
    background: #8B5CF6;
    border: none;
    padding: 10px 15px;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-box button:hover {
    background: #7C3AED;
}

.renungan-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.renungan-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.renungan-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 60px rgba(139, 92, 246, 0.2);
}

.card-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.renungan-thumbnail {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.renungan-card:hover .renungan-thumbnail {
    transform: scale(1.1);
}

.card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 92, 246, 0.8), rgba(124, 58, 237, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.renungan-card:hover .card-overlay {
    opacity: 1;
}

.read-more {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.card-content {
    padding: 30px;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
}

.card-date {
    color: #64748b;
}

.card-category {
    background: #8B5CF6;
    color: white;
    padding: 3px 12px;
    border-radius: 15px;
    font-weight: 500;
}

.card-title {
    margin-bottom: 20px;
}

.card-title a {
    color: #334155;
    text-decoration: none;
    font-size: 1.3rem;
    font-weight: 600;
    line-height: 1.3;
    transition: color 0.3s ease;
}

.card-title a:hover {
    color: #8B5CF6;
}

.card-scripture {
    background: #f1f5f9;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    border-left: 3px solid #8B5CF6;
}

.scripture-text {
    display: block;
    font-style: italic;
    color: #475569;
    line-height: 1.5;
    margin-bottom: 8px;
}

.scripture-ref {
    color: #8B5CF6;
    font-size: 14px;
    font-weight: 600;
}

.card-excerpt {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 20px;
}

.card-author {
    font-size: 14px;
    color: #64748b;
    margin-bottom: 20px;
}

.author-by {
    margin-right: 5px;
}

.author-name {
    color: #8B5CF6;
    font-weight: 600;
}

.card-actions {
    text-align: center;
}

.read-more-btn {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.read-more-btn:hover {
    background: linear-gradient(135deg, #7C3AED, #6D28D9);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
    color: white;
}

.arrow {
    transition: transform 0.3s ease;
}

.read-more-btn:hover .arrow {
    transform: translateX(5px);
}

.no-posts {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
}

.no-posts-content {
    max-width: 400px;
    margin: 0 auto;
}

.no-posts h3 {
    color: #334155;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.no-posts p {
    color: #64748b;
    margin-bottom: 30px;
    line-height: 1.6;
}

.renungan-pagination {
    display: flex;
    justify-content: center;
    margin-bottom: 60px;
}

.renungan-pagination .page-numbers {
    padding: 10px 15px;
    margin: 0 5px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    color: #64748b;
    text-decoration: none;
    transition: all 0.3s ease;
}

.renungan-pagination .page-numbers:hover,
.renungan-pagination .page-numbers.current {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.renungan-cta {
    background: linear-gradient(135deg, #8B5CF6, #7C3AED);
    color: white;
    padding: 60px 40px;
    border-radius: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.renungan-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
}

.cta-content {
    position: relative;
    z-index: 2;
}

.renungan-cta h3 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 15px;
    font-family: 'Poppins', sans-serif;
}

.renungan-cta p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    justify-content: center;
    gap: 15px;
    max-width: 400px;
    margin: 0 auto;
}

.newsletter-form input[type="email"] {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    outline: none;
}

.newsletter-form .btn {
    white-space: nowrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .renungan-title {
        font-size: 2rem;
    }
    
    .archive-title {
        font-size: 2.2rem;
    }
    
    .renungan-filters {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .search-box input[type="search"] {
        min-width: 200px;
    }
    
    .renungan-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-links {
        grid-template-columns: 1fr;
    }
    
    .nav-next {
        text-align: left;
    }
    
    .share-buttons {
        justify-content: center;
    }
    
    .newsletter-form {
        flex-direction: column;
    }
    
    .renungan-header,
    .renungan-content,
    .renungan-prayer,
    .renungan-share,
    .renungan-navigation {
        padding: 20px;
    }
    
    .renungan-scripture {
        margin: -10px 20px 20px;
    }
}
