<?php
/**
 * API Endpoints for Elshadaifm Renungan API
 * Refactored for better error handling and structure
 */

class Elshadaifm_Renungan_API_Endpoints {

    private $db_manager;
    private $openai_service;
    private $validator;
    private $auth;
    private $logger;
    private $helper;

    public function __construct($db_manager, $openai_service) {
        $this->db_manager = $db_manager;
        $this->openai_service = $openai_service;
        $this->validator = new Elshadaifm_Renungan_Validator();
        $this->auth = new Elshadaifm_Renungan_Auth();
        $this->logger = new Elshadaifm_Renungan_Logger();
        $this->helper = new Elshadaifm_Renungan_Helper();
    }

    // Route Registration
    public function register_routes() {
        register_rest_route('elshadaifm-renungan/v1', '/submit', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_submission'),
            'permission_callback' => array($this, 'api_key_auth')
        ));

        register_rest_route('elshadaifm-renungan/v1', '/generate-details', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_generate_details'),
            'permission_callback' => array($this, 'api_key_auth')
        ));

        register_rest_route('elshadaifm-renungan/v1', '/generate-content', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_generate_content'),
            'permission_callback' => array($this, 'api_key_auth')
        ));

        register_rest_route('elshadaifm-renungan/v1', '/generate-image', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_generate_image'),
            'permission_callback' => array($this, 'api_key_auth')
        ));
    }

    // Authentication
    public function api_key_auth($request) {
        return $this->auth->authenticate($request);
    }

    // Endpoint Handlers
    public function handle_submission($request) {
        try {
            $this->logger->log_api_request('submit', 'POST', $request->get_params());

            $params = $request->get_params();
            $content = $this->validator->validate_content($params['content']);

            $content_hash = $this->helper->generate_hash($content);

            if ($this->db_manager->content_exists($content_hash)) {
                $this->logger->log_warning('Duplicate content attempt: ' . substr($content_hash, 0, 10));
                return $this->helper->error_response('This content already exists', 409);
            }

            $result = $this->db_manager->save_renungan($content_hash, $content);

            if ($result === false) {
                $this->logger->log_error('Failed to save content', array('content_hash' => $content_hash));
                return $this->helper->error_response('Failed to save content', 500);
            }

            $response = $this->helper->success_response('Renungan saved successfully', array(
                'id' => $result,
                'hash' => $content_hash
            ));

            $this->logger->log_api_response('submit', 200, $response);
            return $response;

        } catch (Exception $e) {
            $this->logger->log_error('Submission error: ' . $e->getMessage());
            return $this->helper->error_response('An unexpected error occurred', 500);
        }
    }

    public function handle_generate_details($request) {
        try {
            $pending_content = $this->db_manager->get_pending_content();

            if (!$pending_content) {
                return new WP_Error('no_pending', 'No pending content found', array('status' => 400));
            }

            $extracted_data = $this->openai_service->extract_content_data($pending_content->raw_content);

            if (!$extracted_data) {
                return new WP_Error('openai_error', 'Failed to extract data using OpenAI', array('status' => 500));
            }

            $details_id = $this->db_manager->save_renungan_details($extracted_data, $pending_content->raw_content);

            if ($details_id === false) {
                return new WP_Error('database_error', 'Failed to save details', array('status' => 500));
            }

            $this->db_manager->update_content_status($pending_content->id, 'processed');

            return $this->helper->success_response('Details generated successfully', array(
                'details_id' => $details_id,
                'extracted_data' => $extracted_data
            ));

        } catch (Exception $e) {
            $this->logger->log_error('Generate details error: ' . $e->getMessage());
            return $this->helper->error_response('An unexpected error occurred while generating details', 500);
        }
    }

    public function handle_generate_content($request) {
        try {
            $pending_content = $this->db_manager->get_pending_content_generation();

            if (!$pending_content) {
                return new WP_Error('no_pending', 'No pending content generation found', array('status' => 400));
            }

            $generated_content = $this->openai_service->generate_renungan_content(
                $pending_content->ayat,
                $pending_content->judul,
                $pending_content->summary
            );

            if (!$generated_content) {
                return new WP_Error('openai_error', 'Failed to generate content using OpenAI', array('status' => 500));
            }

            $updated = $this->db_manager->update_content_generation(
                $pending_content->id,
                $generated_content
            );

            if (!$updated) {
                return new WP_Error('database_error', 'Failed to update generated content', array('status' => 500));
            }

            return $this->helper->success_response('Content generated successfully', array(
                'id' => $pending_content->id,
                'generated_content' => $generated_content
            ));

        } catch (Exception $e) {
            $this->logger->log_error('Generate content error: ' . $e->getMessage());
            return $this->helper->error_response('An unexpected error occurred while generating content', 500);
        }
    }

    public function handle_generate_image($request) {
        try {
            $this->logger->log_api_request('generate-image', 'POST', array('content_id' => 'pending'));

            $pending_image = $this->db_manager->get_pending_image_generation();

            if (!$pending_image) {
                $this->logger->log('No pending image generation found', Elshadaifm_Renungan_Logger::WARNING);
                return $this->helper->error_response('No pending image generation found', 400);
            }

            $this->logger->log_content_processing($pending_image->id, 'image_prompt_generation');

            $image_prompt = $this->openai_service->generate_image_prompt(
                $pending_image->ayat,
                $pending_image->judul,
                $pending_image->summary,
                $pending_image->content
            );

            if (!$image_prompt) {
                $this->logger->log_openai_error('image_prompt', 'Failed to generate image prompt');
                return $this->helper->error_response('Failed to generate image prompt using OpenAI', 500);
            }

            $start_time = microtime(true);
            $image_data = $this->openai_service->generate_image_with_gpt_image($image_prompt);
            $generation_time = microtime(true) - $start_time;

            if (!$image_data) {
                $this->logger->log_openai_error('image_generation', 'Failed to generate image');
                return $this->helper->error_response('Failed to generate image using GPT Image-1', 500);
            }

            $updated = $this->db_manager->update_image_generation(
                $pending_image->id,
                $image_data,
                $image_prompt
            );

            if (!$updated) {
                $this->logger->log_error('Failed to update generated image', array('content_id' => $pending_image->id));
                return $this->helper->error_response('Failed to update generated image', 500);
            }

            $this->logger->log_image_generation(strlen($image_prompt), $image_data, $generation_time);

            $response = $this->helper->success_response('Image generated successfully', array(
                'id' => $pending_image->id,
                'attachment_id' => $image_data,
                'image_prompt' => $image_prompt
            ));

            $this->logger->log_api_response('generate-image', 200, $response);
            return $response;

        } catch (Exception $e) {
            $this->logger->log_error('Image generation error: ' . $e->getMessage());
            return $this->helper->error_response('An unexpected error occurred', 500);
        }
    }

    // Private Methods - Authentication
    // Authentication methods moved to Elshadaifm_Renungan_Auth module

    // Private Methods - Parameter Validation
    // Parameter validation moved to Elshadaifm_Renungan_Validator module

    // Private Methods - Response Formatting
    // Response formatting moved to Elshadaifm_Renungan_Helper module
    // Exception handling moved to Elshadaifm_Renungan_Logger module
}