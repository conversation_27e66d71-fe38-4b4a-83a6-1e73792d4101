/* ==========================================================================
   ABOUT US - PERFORMANCE OPTIMIZATIONS
   ========================================================================== */

/**
 * About Us Performance Module
 * Handles performance optimizations, lazy loading, and resource management
 */
window.AboutUsPerformance = (function() {
    'use strict';

    // Private variables
    let isInitialized = false;
    let performanceObserver = null;
    let scrollTimeout = null;

    // Public API
    const api = {
        init: init,
        destroy: destroy,
        measurePerformance: measurePerformance,
        optimizeImages: optimizeImages
    };

    /**
     * Initialize performance optimizations
     */
    function init() {
        if (isInitialized) return;

        console.log('AboutUsPerformance: Initializing...');

        // Setup performance monitoring
        setupPerformanceMonitoring();

        // Setup scroll optimizations
        setupScrollOptimizations();

        // Setup image optimizations
        optimizeImages();

        // Setup error handling
        setupErrorHandling();

        // Setup resource hints
        setupResourceHints();

        isInitialized = true;
        console.log('AboutUsPerformance: Initialized successfully');
    }

    /**
     * Destroy performance optimizations
     */
    function destroy() {
        if (performanceObserver) {
            performanceObserver.disconnect();
            performanceObserver = null;
        }

        if (scrollTimeout) {
            cancelAnimationFrame(scrollTimeout);
            scrollTimeout = null;
        }

        isInitialized = false;
    }

    /**
     * Setup performance monitoring
     */
    function setupPerformanceMonitoring() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            measurePerformance();
        });

        // Monitor long tasks (if supported)
        if ('PerformanceObserver' in window) {
            try {
                performanceObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    entries.forEach(entry => {
                        if (entry.duration > 50) {
                            console.warn('Long task detected:', entry.duration + 'ms');
                        }
                    });
                });

                performanceObserver.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                console.warn('Performance monitoring not supported');
            }
        }
    }

    /**
     * Measure and log performance metrics
     */
    function measurePerformance() {
        if (!('performance' in window)) return;

        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
            const metrics = {
                'DNS Lookup': navigation.domainLookupEnd - navigation.domainLookupStart,
                'TCP Connection': navigation.connectEnd - navigation.connectStart,
                'Request': navigation.responseStart - navigation.requestStart,
                'Response': navigation.responseEnd - navigation.responseStart,
                'DOM Processing': navigation.domContentLoadedEventStart - navigation.responseEnd,
                'Load Complete': navigation.loadEventEnd - navigation.loadEventStart,
                'Total Load Time': navigation.loadEventEnd - navigation.navigationStart
            };

            console.group('About Us Page Performance');
            Object.entries(metrics).forEach(([key, value]) => {
                console.log(`${key}: ${Math.round(value)}ms`);
            });
            console.groupEnd();
        }

        // Measure First Contentful Paint
        const fcpEntries = performance.getEntriesByName('first-contentful-paint');
        if (fcpEntries.length > 0) {
            console.log(`First Contentful Paint: ${Math.round(fcpEntries[0].startTime)}ms`);
        }

        // Measure Largest Contentful Paint
        if ('PerformanceObserver' in window) {
            try {
                const lcpObserver = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    const lastEntry = entries[entries.length - 1];
                    console.log(`Largest Contentful Paint: ${Math.round(lastEntry.startTime)}ms`);
                });

                lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            } catch (e) {
                // LCP not supported
            }
        }
    }

    /**
     * Setup scroll optimizations
     */
    function setupScrollOptimizations() {
        // Throttle scroll events for better performance
        window.addEventListener('scroll', function() {
            if (scrollTimeout) {
                cancelAnimationFrame(scrollTimeout);
            }

            scrollTimeout = requestAnimationFrame(function() {
                // Handle scroll-based animations here
                // This is already optimized with requestAnimationFrame
            });
        }, { passive: true });
    }

    /**
     * Optimize images with lazy loading
     */
    function optimizeImages() {
        // Use native lazy loading if supported
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if ('loading' in HTMLImageElement.prototype) {
                img.loading = 'lazy';
            }
        });

        // Intersection Observer fallback for older browsers
        if ('IntersectionObserver' in window && !('loading' in HTMLImageElement.prototype)) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            imageObserver.unobserve(img);
                        }
                    }
                });
            });

            images.forEach(img => {
                if (img.dataset.src) {
                    imageObserver.observe(img);
                }
            });
        }
    }

    /**
     * Setup error handling for performance
     */
    function setupErrorHandling() {
        // Handle timeline errors gracefully
        try {
            const timelineItems = document.querySelectorAll('.timeline-item');
            if (timelineItems.length === 0) {
                console.warn('Timeline elements not found - performance monitoring limited');
            }

            // Fallback for browsers that don't support Intersection Observer
            if (!window.IntersectionObserver) {
                console.warn('IntersectionObserver not supported - using fallback animations');
                timelineItems.forEach(item => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                    item.classList.add('visible');
                });
            }
        } catch (error) {
            console.error('Timeline initialization error:', error);
        }
    }

    /**
     * Setup resource hints for better loading
     */
    function setupResourceHints() {
        // Get WordPress template directory URI for proper path handling
        const templateDir = aboutUsData.templateDir || '/elshadaifm/wp-content/themes/elshadaifm';

        // Preload critical resources
        const criticalResources = [
            templateDir + '/assets/css/about-us/about-timeline.css',
            templateDir + '/assets/js/about-us/about-timeline.js'
        ];

        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }

    /**
     * Optimize animations for better performance
     */
    function optimizeAnimations() {
        // Use CSS transforms instead of changing layout properties
        const animatedElements = document.querySelectorAll('.timeline-item, .highlight-card');
        
        animatedElements.forEach(element => {
            // Force hardware acceleration
            element.style.willChange = 'transform, opacity';
            
            // Clean up will-change after animation
            element.addEventListener('transitionend', function() {
                this.style.willChange = 'auto';
            });
        });
    }

    /**
     * Setup memory management
     */
    function setupMemoryManagement() {
        // Clean up event listeners on page unload
        window.addEventListener('beforeunload', () => {
            destroy();
        });

        // Monitor memory usage (if supported)
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
                    console.warn('High memory usage detected');
                }
            }, 30000); // Check every 30 seconds
        }
    }

    /**
     * Setup passive event listeners
     */
    function setupPassiveListeners() {
        // Check for passive event listener support
        let passiveSupported = false;
        try {
            const options = {
                get passive() {
                    passiveSupported = true;
                    return false;
                }
            };
            window.addEventListener('test', null, options);
            window.removeEventListener('test', null, options);
        } catch (err) {
            passiveSupported = false;
        }

        // Use passive listeners where appropriate
        const passiveEvents = ['scroll', 'touchstart', 'touchmove', 'wheel'];
        passiveEvents.forEach(eventType => {
            document.addEventListener(eventType, function() {
                // Event handler
            }, passiveSupported ? { passive: true } : false);
        });
    }

    /**
     * Debounce function for performance
     */
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    /**
     * Throttle function for performance
     */
    function throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Initialize additional optimizations
    setTimeout(() => {
        optimizeAnimations();
        setupMemoryManagement();
        setupPassiveListeners();
    }, 100);

    return api;
})();
