<?php
/**
 * Utility Functions for Elshadaifm Renungan AI
 *
 * @package ElshadaifmRenunganAI
 * @subpackage Includes
 */

if (!defined('ABSPATH')) {
    exit;
}

class EfraI_Utils {

    /**
     * Attach base64 image to post
     */
    public function attach_base64_image_to_post($base64_image, $post_id) {
        // Upload dir
        $upload_dir = wp_upload_dir();
        $filename = 'renungan-' . $post_id . '-' . time() . '.jpg';
        $filepath = $upload_dir['path'] . '/' . $filename;

        // Save the file
        $image_data = base64_decode($base64_image);
        if (!$image_data) {
            throw new Exception('Failed to decode base64 image');
        }

        file_put_contents($filepath, $image_data);

        // Check file type
        $filetype = wp_check_filetype_and_ext($filepath, $filename);
        if (strpos($filetype['type'], 'image') === false) {
            unlink($filepath);
            throw new Exception('Invalid image file type');
        }

        // Prepare attachment
        $attachment = array(
            'post_mime_type' => $filetype['type'],
            'post_title' => preg_replace('/\.[^.]+$/', '', $filename),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        // Insert attachment
        $attach_id = wp_insert_attachment($attachment, $filepath, $post_id);

        // Generate metadata
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        $attach_data = wp_generate_attachment_metadata($attach_id, $filepath);
        wp_update_attachment_metadata($attach_id, $attach_data);

        return $attach_id;
    }

    /**
     * Get latest renungan posts
     */
    public function get_latest_renungan($limit = 5) {
        $args = array(
            'post_type' => 'renungan',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'orderby' => 'date',
            'order' => 'DESC'
        );

        return get_posts($args);
    }

    /**
     * Check if post has AI generated image
     */
    public function has_ai_image($post_id) {
        $generated_image = get_post_meta($post_id, '_efrai_generated_image', true);
        return !empty($generated_image);
    }

    /**
     * Get renungan meta data
     */
    public function get_renungan_meta($post_id) {
        return array(
            'tanggal' => get_post_meta($post_id, '_efrai_tanggal', true),
            'ayat' => get_post_meta($post_id, '_efrai_ayat', true),
            'tema' => get_post_meta($post_id, '_efrai_tema', true),
            'has_ai_image' => $this->has_ai_image($post_id)
        );
    }

    /**
     * Format file size
     */
    public function format_file_size($bytes) {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = array('Bytes', 'KB', 'MB', 'GB');
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * Safe output for HTML
     */
    public function safe_output($text) {
        return wp_kses($text, array(
            'p' => array(),
            'br' => array(),
            'strong' => array(),
            'em' => array(),
            'h1' => array(), 'h2' => array(), 'h3' => array(), 'h4' => array(), 'h5' => array(), 'h6' => array(),
            'ul' => array(), 'ol' => array(), 'li' => array(),
            'blockquote' => array(),
            'a' => array('href' => array(), 'target' => array())
        ));
    }
}