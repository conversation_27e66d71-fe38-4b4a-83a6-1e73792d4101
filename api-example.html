<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Renungan</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: monospace;
            margin-bottom: 10px;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test API Submit Renungan</h1>

        <div>
            <label>WordPress Username:</label>
            <input type="text" id="username" value="admin" placeholder="Username">
        </div>

        <div>
            <label>WordPress Password:</label>
            <input type="password" id="password" placeholder="Password">
        </div>

        <div>
            <label>Content Renungan:</label>
            <textarea id="content" placeholder="Paste content renungan di sini...">RENUNGAN KELUARGA ALLAH
BIMBINGAN ROHANI – Take Over Mode #3
22-28 September 2025
SENIN, 22 SEPTEMBER 2025
Sadari dimensi ROH itu NYATA, maka engkau akan MENANG.
1 Yohanes 5:4 Sebab semua yang lahir dari Allah, mengalahkan dunia. Dan inilah kemenangan yang
mengalahkan dunia: iman kita.
SIANG
Iman yang BERKOBAR-KOBAR akan membawamu dari KEMENANGAN kepada kemenangan.
1 Korintus 15:57 Tetapi syukur kepada Allah, yang telah memberikan kepada kita kemenangan oleh Yesus
Kristus, Tuhan kita.
MALAM
Masalah yang tampak sering BERAKAR dari PEPERANGAN ROHANI yang tak tampak.
Efesus 6:12 Karena perjuangan kita bukanlah melawan darah dan daging, tetapi melawan pemerintah-
pemerintah, melawan penguasa-penguasa dunia yang gelap ini, melawan roh-roh jahat di udara.</textarea>
        </div>

        <button onclick="submitRenungan()">Submit Renungan</button>

        <div id="result"></div>
    </div>

    <script>
        async function submitRenungan() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const content = document.getElementById('content').value;
            const resultDiv = document.getElementById('result');

            if (!username || !password || !content) {
                resultDiv.innerHTML = '<div class="result error">Please fill all fields</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="result info">Submitting...</div>';

            try {
                const response = await fetch('/elshadaifm/wp-json/elshadaifm-renungan/v1/submit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Basic ' + btoa(username + ':' + password)
                    },
                    body: JSON.stringify({
                        content: content
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    resultDiv.innerHTML = '<div class="result success">✅ Success!\n\n' + JSON.stringify(data, null, 2) + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="result error">❌ Error ' + response.status + '\n\n' + JSON.stringify(data, null, 2) + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<div class="result error">❌ Network Error: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>