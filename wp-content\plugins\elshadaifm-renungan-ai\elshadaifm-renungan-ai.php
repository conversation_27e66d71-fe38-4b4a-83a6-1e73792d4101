<?php
/**
 * Plugin Name: Elshadaifm Renungan AI
 * Plugin URI: https://elshadaifm.local
 * Description: Plugin untuk mengelola renungan dengan integrasi OpenAI untuk generate konten dan gambar
 * Version: 1.0.0
 * Author: Claude Code
 * Text Domain: elshadaifm-renungan-ai
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('EFRAI_VERSION', '1.0.0');
define('EFRAI_PLUGIN_URL', plugin_dir_url(__FILE__));
define('EFRAI_PLUGIN_PATH', plugin_dir_path(__FILE__));

// Include required files
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-ajax-handlers.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-date-utils.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-openai.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-google-ai.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-utils.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-admin-pages.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-shortcodes.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-post-types.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-meta-boxes.php';
require_once EFRAI_PLUGIN_PATH . 'includes/class-efrai-bulk-actions.php';

// Main plugin class
class ElshadaifmRenunganAI {

    private $openai_api_key;
    private $openai_model = 'gpt-4o-mini';
    private $google_ai_key;

    // Module instances
    protected $ajax_handlers;
    protected $date_utils;
    protected $openai;
    protected $google_ai;
    protected $utils;
    protected $admin_pages;
    protected $shortcodes;
    protected $post_types;
    protected $meta_boxes;
    protected $bulk_actions;

    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    public function init() {
        // Load text domain
        load_plugin_textdomain('elshadaifm-renungan-ai', false, dirname(plugin_basename(__FILE__)) . '/languages');

        // Get API keys
        $this->openai_api_key = get_option('efrai_openai_api_key', '');
        $this->google_ai_key = get_option('efrai_google_ai_key', '');

        // Initialize modules
        $this->initialize_modules();

        // Register post types
        $this->post_types->register_post_types();

        // Add admin menus (only in admin)
        if (is_admin()) {
            add_action('admin_menu', array($this->admin_pages, 'add_admin_menu'));
        }

        // Register admin settings (only in admin)
        if (is_admin()) {
            add_action('admin_init', array($this->admin_pages, 'register_settings'));
        }

        // Register shortcodes
        $this->shortcodes->register_shortcodes();

        // Add meta boxes (only in admin)
        if (is_admin()) {
            add_action('add_meta_boxes', array($this->meta_boxes, 'add_meta_boxes'));
            add_action('save_post', array($this->meta_boxes, 'save_meta_box'));
        }

        // Add bulk actions (only in admin)
        if (is_admin()) {
            add_filter('bulk_actions-edit-renungan', array($this->bulk_actions, 'add_bulk_actions'));
            add_filter('handle_bulk_actions-edit-renungan', array($this->bulk_actions, 'handle_bulk_actions'), 10, 3);
            add_filter('manage_renungan_posts_columns', array($this->bulk_actions, 'add_custom_columns'));
            add_action('manage_renungan_posts_custom_column', array($this->bulk_actions, 'display_custom_columns'), 10, 2);
            add_filter('manage_edit-renungan_sortable_columns', array($this->bulk_actions, 'make_custom_columns_sortable'));
            add_action('bulk_edit_custom_box', array($this->bulk_actions, 'add_quick_edit_fields'), 10, 2);
            add_action('admin_notices', array($this->bulk_actions, 'add_admin_notice'));
        }

        // Enqueue admin scripts
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Add settings link
        add_filter('plugin_action_links_' . plugin_basename(__FILE__), array($this, 'add_settings_link'));

        // Template filters
        add_filter('template_include', array($this->post_types, 'template_loader'));

        // Register AJAX handlers through module
        $this->ajax_handlers->register_ajax_handlers();
    }

    /**
     * Initialize modules
     */
    private function initialize_modules() {
        $this->date_utils = new Efrai_Date_Utils();
        $this->openai = new EfraI_OpenAI($this->openai_api_key, $this->openai_model);
        $this->google_ai = new EfraI_Google_AI($this->google_ai_key);
        $this->utils = new EfraI_Utils();
        $this->ajax_handlers = new EfraI_AJAX_Handlers($this);
        $this->admin_pages = new EfraI_Admin_Pages($this);
        $this->shortcodes = new EfraI_Shortcodes($this);
        $this->post_types = new EfraI_Post_Types();
        $this->meta_boxes = new EfraI_Meta_Boxes($this->utils);
        $this->bulk_actions = new EfraI_Bulk_Actions();
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Register post types
        $this->post_types->register_post_types();

        // Flush rewrite rules
        flush_rewrite_rules();

        // Create default options
        if (!get_option('efrai_openai_model')) {
            add_option('efrai_openai_model', 'gpt-4o-mini');
        }
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        flush_rewrite_rules();
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        // Enqueue on all admin pages for now
        wp_enqueue_script('efrai-admin-script', EFRAI_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), EFRAI_VERSION, true);

        // Localize script
        wp_localize_script('efrai-admin-script', 'efrai_admin_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('efrai_admin_nonce')
        ));

        // Enqueue admin styles
        wp_enqueue_style('efrai-admin-style', EFRAI_PLUGIN_URL . 'assets/css/admin.css', array(), EFRAI_VERSION);
    }

    /**
     * Add settings link
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=efrai-settings') . '">' . __('Settings', 'elshadaifm-renungan-ai') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    // DELEGATED METHODS TO MODULES

    /**
     * Test OpenAI connection
     */
    public function test_openai_connection() {
        return $this->openai->test_connection();
    }

    /**
     * Test Google AI connection
     */
    public function test_google_connection() {
        return $this->google_ai->test_connection();
    }

    /**
     * Generate renungan from OpenAI
     */
    public function generate_renungan_from_openai($tanggal, $input_content, $custom_title = null) {
        return $this->openai->generate_renungan_content($tanggal, $input_content, $custom_title);
    }

    /**
     * Generate image from Google AI
     */
    public function generate_image_from_google_ai($prompt) {
        return $this->google_ai->generate_image($prompt);
    }

    /**
     * Attach base64 image to post
     */
    public function attach_base64_image_to_post($image_data, $post_id) {
        return $this->utils->attach_base64_image_to_post($image_data, $post_id);
    }

    /**
     * Extract date from content
     */
    public function extract_date_from_content($content) {
        return $this->date_utils->extract_date_from_content($content);
    }

    /**
     * Extract multiple dates from content
     */
    public function extract_multiple_dates_from_content($content) {
        return $this->date_utils->extract_multiple_dates_from_content($content);
    }

    /**
     * Get renungan by date
     */
    public function get_renungan_by_date($date) {
        return $this->date_utils->get_renungan_by_date($date);
    }

    /**
     * Generate content preview for date
     */
    public function generate_content_preview_for_date($content, $date) {
        return $this->date_utils->generate_content_preview_for_date($content, $date);
    }

    /**
     * Generate title for date
     */
    public function generate_title_for_date($date) {
        return $this->date_utils->generate_title_for_date($date);
    }

    /**
     * Get modules - public getter methods
     */
    public function get_ajax_handlers() {
        return $this->ajax_handlers;
    }

    public function get_date_utils() {
        return $this->date_utils;
    }

    public function get_openai() {
        return $this->openai;
    }

    public function get_google_ai() {
        return $this->google_ai;
    }

    public function get_utils() {
        return $this->utils;
    }

    public function get_admin_pages() {
        return $this->admin_pages;
    }

    public function get_shortcodes() {
        return $this->shortcodes;
    }

    public function get_post_types() {
        return $this->post_types;
    }

    public function get_meta_boxes() {
        return $this->meta_boxes;
    }

    public function get_bulk_actions() {
        return $this->bulk_actions;
    }
}

// Initialize the plugin
function efrai_plugin() {
    global $efrai_plugin;
    $efrai_plugin = new ElshadaifmRenunganAI();
}

// Hook for plugin initialization
add_action('plugins_loaded', 'efrai_plugin');

// Legacy functions for backward compatibility
function efrai_has_ai_image($post_id) {
    global $efrai_plugin;
    return $efrai_plugin->get_utils()->has_ai_image($post_id);
}

function efrai_get_renungan_meta($post_id) {
    global $efrai_plugin;
    return $efrai_plugin->get_utils()->get_renungan_meta($post_id);
}

function efrai_get_latest_renungan($limit = 5) {
    global $efrai_plugin;
    return $efrai_plugin->get_utils()->get_latest_renungan($limit);
}