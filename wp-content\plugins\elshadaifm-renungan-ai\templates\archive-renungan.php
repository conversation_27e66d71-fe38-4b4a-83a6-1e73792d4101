<?php
/**
 * Template for displaying renungan archive
 */

get_header();
?>

<div class="efrai-container">
    <div class="efrai-main-content">
        <header class="efrai-archive-header">
            <h1 class="efrai-archive-title"><?php post_type_archive_title(); ?></h1>
            <p class="efrai-archive-description">
                <?php echo esc_html__('Kumpulan renungan harian untuk memperkuat iman <PERSON>', 'elshadaifm-renungan-ai'); ?>
            </p>
        </header>

        <?php if (have_posts()) : ?>

            <div class="efrai-renungan-grid">
                <?php while (have_posts()) : the_post(); ?>

                    <article id="post-<?php the_ID(); ?>" <?php post_class('efrai-renungan-card'); ?>>
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="efrai-card-image">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium'); ?>
                                </a>
                            </div>
                        <?php endif; ?>

                        <div class="efrai-card-content">
                            <header class="efrai-card-header">
                                <h2 class="efrai-card-title">
                                    <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                </h2>

                                <div class="efrai-card-meta">
                                    <?php if ($tanggal = get_post_meta(get_the_ID(), '_efrai_tanggal', true)) : ?>
                                        <span class="efrai-meta-date">
                                            <span class="dashicons dashicons-calendar-alt"></span>
                                            <?php echo esc_html($tanggal); ?>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($tema = get_post_meta(get_the_ID(), '_efrai_tema', true)) : ?>
                                        <span class="efrai-meta-theme">
                                            <span class="dashicons dashicons-tag"></span>
                                            <?php echo esc_html($tema); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </header>

                            <div class="efrai-card-excerpt">
                                <?php the_excerpt(); ?>
                            </div>

                            <div class="efrai-card-footer">
                                <a href="<?php the_permalink(); ?>" class="efrai-read-more">
                                    <?php echo esc_html__('Baca Selengkapnya', 'elshadaifm-renungan-ai'); ?>
                                    <span class="dashicons dashicons-arrow-right-alt2"></span>
                                </a>
                            </div>
                        </div>
                    </article>

                <?php endwhile; ?>
            </div>

            <div class="efrai-pagination">
                <?php the_posts_pagination(array(
                    'mid_size' => 2,
                    'prev_text' => __('&laquo; Previous', 'elshadaifm-renungan-ai'),
                    'next_text' => __('Next &raquo;', 'elshadaifm-renungan-ai'),
                )); ?>
            </div>

        <?php else : ?>

            <div class="efrai-no-content">
                <h2><?php echo esc_html__('Belum Ada Renungan', 'elshadaifm-renungan-ai'); ?></h2>
                <p><?php echo esc_html__('Renungan akan segera tersedia. Kunjungi kembali beberapa saat lagi.', 'elshadaifm-renungan-ai'); ?></p>
                <?php if (current_user_can('edit_posts')) : ?>
                    <a href="<?php echo admin_url('admin.php?page=efrai-generate'); ?>" class="button button-primary">
                        <?php echo esc_html__('Generate Renungan Pertama', 'elshadaifm-renungan-ai'); ?>
                    </a>
                <?php endif; ?>
            </div>

        <?php endif; ?>
    </div>

    <?php get_sidebar(); ?>
</div>

<?php get_footer(); ?>