# About Us JavaScript Modules

## 📁 **Struktur Modular**

File JavaScript About Us telah dipecah menjadi 6 modul terpisah untuk kemudahan maintenance dan pengembangan:

```
assets/js/
├── about-us.js                    # Main controller (155 lines)
└── about-us/
    ├── about-base.js              # Base functionality (170 lines)
    ├── about-animations.js        # Animation classes (200 lines)
    ├── about-timeline.js          # Timeline functionality (280 lines)
    ├── about-interactions.js      # Mouse interactions (180 lines)
    ├── about-mobile.js           # Mobile functionality (220 lines)
    ├── about-performance.js       # Performance optimizations (250 lines)
    └── README.md                  # This documentation
```

## 🎯 **Deskripsi Modul**

### 1. **about-base.js** (Base Functionality)
**Dependencies:** jQuery
**Exports:** `window.AboutUsBase`

**Fungsi:**
- ✅ Setup dasar dan utilities
- ✅ Mobile detection
- ✅ Smooth scrolling
- ✅ Keyboard navigation
- ✅ Error handling
- ✅ Helper functions (debounce, throttle, viewport detection)

**API:**
```javascript
AboutUsBase.init()
AboutUsBase.isMobile()
AboutUsBase.utils.debounce(func, wait)
AboutUsBase.utils.throttle(func, limit)
```

### 2. **about-animations.js** (Animations & Observers)
**Dependencies:** about-base.js
**Exports:** `window.AboutUsAnimations`

**Fungsi:**
- ✅ Intersection Observer setup
- ✅ Parallax effects
- ✅ Section reveal animations
- ✅ Loading animations
- ✅ Stat number counter animations
- ✅ AOS (Animate On Scroll) functionality

**API:**
```javascript
AboutUsAnimations.init()
AboutUsAnimations.destroy()
AboutUsAnimations.animateStatNumber(element)
```

### 3. **about-timeline.js** (Timeline Functionality)
**Dependencies:** about-base.js, about-animations.js
**Exports:** `window.AboutUsTimeline`

**Fungsi:**
- ✅ Timeline item animations
- ✅ Progress indicator (desktop only)
- ✅ Timeline interactions
- ✅ Scroll-based progress tracking
- ✅ Clickable navigation dots
- ✅ Fallback mechanisms

**API:**
```javascript
AboutUsTimeline.init()
AboutUsTimeline.destroy()
AboutUsTimeline.showItems()
AboutUsTimeline.hideItems()
```

### 4. **about-interactions.js** (Mouse Interactions)
**Dependencies:** about-base.js
**Exports:** `window.AboutUsInteractions`

**Fungsi:**
- ✅ Card hover effects dengan 3D transform
- ✅ Button ripple effects
- ✅ Value card icon animations
- ✅ Focus interactions untuk accessibility
- ✅ Touch interactions untuk mobile
- ✅ Custom cursor effects

**API:**
```javascript
AboutUsInteractions.init()
AboutUsInteractions.destroy()
```

### 5. **about-mobile.js** (Mobile Functionality)
**Dependencies:** about-base.js
**Exports:** `window.AboutUsMobile`

**Fungsi:**
- ✅ Mobile timeline handling
- ✅ Responsive layout adjustments
- ✅ Touch feedback
- ✅ Swipe gestures
- ✅ Mobile navigation
- ✅ Orientation change handling
- ✅ Viewport handling (keyboard show/hide)

**API:**
```javascript
AboutUsMobile.init()
AboutUsMobile.destroy()
AboutUsMobile.handleResize()
```

### 6. **about-performance.js** (Performance Optimizations)
**Dependencies:** about-base.js
**Exports:** `window.AboutUsPerformance`

**Fungsi:**
- ✅ Performance monitoring
- ✅ Scroll optimizations
- ✅ Image lazy loading
- ✅ Error handling
- ✅ Resource hints
- ✅ Memory management
- ✅ Passive event listeners

**API:**
```javascript
AboutUsPerformance.init()
AboutUsPerformance.destroy()
AboutUsPerformance.measurePerformance()
AboutUsPerformance.optimizeImages()
```

## 🚀 **Initialization Order**

Modul diinisialisasi dalam urutan dependency:

1. **Base** → Setup dasar dan utilities
2. **Performance** → Optimizations dan monitoring
3. **Animations** → Intersection observers dan effects
4. **Timeline** → Timeline functionality (depends on Base + Animations)
5. **Interactions** → Mouse dan touch interactions
6. **Mobile** → Mobile-specific features (should be last)

## 🔧 **Development**

### **Loading Modules**
Modules dimuat melalui `wp_enqueue_script()` di `page-about-us.php`:

```php
// Dependencies loaded first
wp_enqueue_script( 'about-us-base', '...', array('jquery') );
wp_enqueue_script( 'about-us-performance', '...', array('about-us-base') );
wp_enqueue_script( 'about-us-animations', '...', array('about-us-base') );
wp_enqueue_script( 'about-us-timeline', '...', array('about-us-base', 'about-us-animations') );
wp_enqueue_script( 'about-us-interactions', '...', array('about-us-base') );
wp_enqueue_script( 'about-us-mobile', '...', array('about-us-base') );

// Main controller loaded last
wp_enqueue_script( 'about-us', '...', array('all-dependencies') );
```

### **Error Handling**
- ✅ Graceful degradation jika modul tidak ter-load
- ✅ Fallback timeline visibility
- ✅ Console logging untuk debugging
- ✅ Emergency fallback setelah 2 detik

### **Debugging**
Dalam development mode (localhost), semua modul di-expose ke:
```javascript
window.AboutUsModules = {
    Base: AboutUsBase,
    Performance: AboutUsPerformance,
    Animations: AboutUsAnimations,
    Timeline: AboutUsTimeline,
    Interactions: AboutUsInteractions,
    Mobile: AboutUsMobile
}
```

## 📊 **Performance Benefits**

| **Sebelum** | **Sesudah** |
|-------------|-------------|
| 1 file (570+ lines) | 6 modular files |
| Sulit debug | Easy debugging |
| Monolithic loading | Selective loading |
| Hard to maintain | Easy maintenance |
| No dependency management | Clear dependencies |

## 🎉 **Features**

### ✅ **Timeline Features**
- Responsive timeline dengan alternating layout
- Progress indicator dengan clickable dots (desktop)
- Smooth scroll animations
- Mobile-optimized vertical layout
- Fallback mechanisms untuk browser compatibility

### ✅ **Animation Features**
- Intersection Observer untuk scroll animations
- Parallax effects pada hero section
- Staggered animations untuk cards
- Counter animations untuk statistics
- Loading state management

### ✅ **Interaction Features**
- 3D hover effects pada cards
- Ripple effects pada buttons
- Touch feedback untuk mobile
- Keyboard navigation support
- Focus indicators untuk accessibility

### ✅ **Performance Features**
- Throttled scroll events
- Passive event listeners
- Image lazy loading
- Memory management
- Performance monitoring

## 🔄 **Migration dari Monolithic**

File lama `about-us.js` (570+ lines) telah berhasil dipecah menjadi:
- **Main controller**: 155 lines
- **6 modular files**: 170-280 lines each
- **Total**: ~1,500 lines (lebih terorganisir)
- **Maintainability**: ⬆️ Significantly improved
- **Collaboration**: ⬆️ Multiple developers can work simultaneously
- **Testing**: ⬆️ Individual modules can be tested separately
