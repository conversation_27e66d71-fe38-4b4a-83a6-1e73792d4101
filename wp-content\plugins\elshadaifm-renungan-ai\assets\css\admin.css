/* Elshadaifm Renungan AI Admin Styles */

.efrai-dashboard .efrai-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.efrai-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.efrai-card h3 {
    margin: 0 0 10px 0;
    color: #23282d;
    font-size: 14px;
    font-weight: 600;
}

.efrai-stat {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
}

.efrai-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.efrai-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.efrai-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.efrai-actions .button {
    margin-bottom: 10px;
}

/* Loading states */
.efrai-loading {
    opacity: 0.6;
    pointer-events: none;
}

.efrai-loading .spinner {
    display: inline-block !important;
    visibility: visible !important;
}

/* Responsive design */
@media screen and (max-width: 782px) {
    .efrai-dashboard .efrai-cards {
        grid-template-columns: 1fr;
    }

    .efrai-actions {
        flex-direction: column;
    }

    .efrai-actions .button {
        width: 100%;
        text-align: center;
    }
}

/* Status indicators */
.efrai-status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.efrai-status-badge.ai-generated {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.efrai-status-badge.manual {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.efrai-status-badge.has-image {
    background: #cce5ff;
    color: #004085;
    border: 1px solid #99d6ff;
}

/* Button enhancements */
.efrai-button-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.efrai-button-help {
    color: #666;
    text-decoration: none;
    font-size: 20px;
    line-height: 1;
}

.efrai-button-help:hover {
    color: #0073aa;
}

/* Form enhancements */
.efrai-form-field {
    margin-bottom: 15px;
}

.efrai-form-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.efrai-form-field input[type="text"],
.efrai-form-field input[type="date"],
.efrai-form-field textarea,
.efrai-form-field select {
    width: 100%;
    max-width: 400px;
}

.efrai-form-field .description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

/* Progress indicators */
.efrai-progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f1;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.efrai-progress-fill {
    height: 100%;
    background: #0073aa;
    transition: width 0.3s ease;
}

.efrai-progress-text {
    text-align: center;
    margin-top: 5px;
    font-size: 12px;
    color: #666;
}

/* Tooltips */
.efrai-tooltip {
    position: relative;
    cursor: help;
}

.efrai-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
}

.efrai-tooltip:hover::after {
    opacity: 1;
}

/* File upload area */
.efrai-upload-zone {
    border: 2px dashed #b4b9be;
    border-radius: 4px;
    padding: 40px;
    text-align: center;
    background: #f8f9f9;
    transition: all 0.3s ease;
}

.efrai-upload-zone.dragover {
    border-color: #0073aa;
    background: #f0f6fc;
}

.efrai-upload-zone:hover {
    border-color: #0073aa;
}

/* Notification styles */
.efrai-notice {
    padding: 10px 15px;
    border-radius: 4px;
    margin: 15px 0;
}

.efrai-notice.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.efrai-notice.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.efrai-notice.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.efrai-notice.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}