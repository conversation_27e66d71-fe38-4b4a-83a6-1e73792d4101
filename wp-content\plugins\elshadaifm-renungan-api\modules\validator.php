<?php
/**
 * Validator Module for Elshadaifm Renungan API
 * Handles input validation and sanitization
 */

class Elshadaifm_Renungan_Validator {

    /**
     * Validate content input
     */
    public static function validate_content($content) {
        if (empty($content)) {
            throw new InvalidArgumentException('Content is required');
        }

        if (!is_string($content)) {
            throw new InvalidArgumentException('Content must be a string');
        }

        if (strlen($content) < 10) {
            throw new InvalidArgumentException('Content must be at least 10 characters long');
        }

        if (strlen($content) > 100000) {
            throw new InvalidArgumentException('Content is too long (max 100KB)');
        }

        // Remove potentially harmful content
        $sanitized = self::sanitize_content($content);

        return $sanitized;
    }

    /**
     * Validate API key format
     */
    public static function validate_api_key($api_key) {
        if (empty($api_key)) {
            throw new InvalidArgumentException('API key is required');
        }

        if (!is_string($api_key)) {
            throw new InvalidArgumentException('API key must be a string');
        }

        if (!preg_match('/^efm_[a-f0-9]{32}$/', $api_key)) {
            throw new InvalidArgumentException('Invalid API key format');
        }

        return $api_key;
    }

    /**
     * Validate extracted data structure
     */
    public static function validate_extracted_data($data) {
        if (empty($data)) {
            throw new InvalidArgumentException('Extracted data cannot be empty');
        }

        if (is_array($data)) {
            foreach ($data as $entry) {
                self::validate_single_entry($entry);
            }
        } else {
            self::validate_single_entry($data);
        }

        return $data;
    }

    /**
     * Validate single entry data
     */
    public static function validate_single_entry($entry) {
        if (!is_array($entry)) {
            throw new InvalidArgumentException('Entry must be an array');
        }

        // Validate tanggal_publish if present
        if (isset($entry['tanggal_publish'])) {
            if (!self::validate_date($entry['tanggal_publish'])) {
                throw new InvalidArgumentException('Invalid tanggal_publish format');
            }
        }

        // Validate ayat if present
        if (isset($entry['ayat'])) {
            if (!is_string($entry['ayat']) || strlen($entry['ayat']) > 500) {
                throw new InvalidArgumentException('Invalid ayat format');
            }
        }

        // Validate judul if present
        if (isset($entry['judul'])) {
            if (!is_string($entry['judul']) || strlen($entry['judul']) > 255) {
                throw new InvalidArgumentException('Invalid judul format');
            }
        }

        // Validate summary if present
        if (isset($entry['summary'])) {
            if (!is_string($entry['summary']) || strlen($entry['summary']) > 2000) {
                throw new InvalidArgumentException('Invalid summary format');
            }
        }
    }

    /**
     * Validate date format
     */
    public static function validate_date($date) {
        if (empty($date)) {
            return true; // Optional field
        }

        $datetime = DateTime::createFromFormat('Y-m-d', $date);
        return $datetime && $datetime->format('Y-m-d') === $date;
    }

    /**
     * Validate generated content
     */
    public static function validate_generated_content($content) {
        if (empty($content)) {
            throw new InvalidArgumentException('Generated content cannot be empty');
        }

        if (!is_string($content)) {
            throw new InvalidArgumentException('Generated content must be a string');
        }

        if (strlen($content) < 50) {
            throw new InvalidArgumentException('Generated content is too short');
        }

        if (strlen($content) > 50000) {
            throw new InvalidArgumentException('Generated content is too long');
        }

        return self::sanitize_content($content);
    }

    /**
     * Validate image prompt
     */
    public static function validate_image_prompt($prompt) {
        if (empty($prompt)) {
            throw new InvalidArgumentException('Image prompt cannot be empty');
        }

        if (!is_string($prompt)) {
            throw new InvalidArgumentException('Image prompt must be a string');
        }

        if (strlen($prompt) < 20) {
            throw new InvalidArgumentException('Image prompt is too short');
        }

        if (strlen($prompt) > 4000) {
            throw new InvalidArgumentException('Image prompt is too long');
        }

        return self::sanitize_content($prompt);
    }

    /**
     * Validate attachment ID
     */
    public static function validate_attachment_id($attachment_id) {
        if (empty($attachment_id)) {
            throw new InvalidArgumentException('Attachment ID is required');
        }

        if (!is_numeric($attachment_id) || $attachment_id <= 0) {
            throw new InvalidArgumentException('Invalid attachment ID');
        }

        return (int) $attachment_id;
    }

    /**
     * Sanitize content
     */
    private static function sanitize_content($content) {
        // Remove HTML tags (if not needed)
        $content = strip_tags($content);

        // Remove extra whitespace
        $content = trim($content);
        $content = preg_replace('/\s+/', ' ', $content);

        return $content;
    }

    /**
     * Validate OpenAI API key
     */
    public static function validate_openai_key($api_key) {
        if (empty($api_key)) {
            throw new InvalidArgumentException('OpenAI API key is required');
        }

        if (!is_string($api_key)) {
            throw new InvalidArgumentException('OpenAI API key must be a string');
        }

        // Basic validation for OpenAI API key format
        if (!preg_match('/^sk-[a-zA-Z0-9_-]+$/', $api_key)) {
            throw new InvalidArgumentException('Invalid OpenAI API key format');
        }

        return $api_key;
    }
}