<?php
/**
 * OpenAI Service for Elshadaifm Renungan API
 * Refactored for better separation of concerns
 */

class Elshadaifm_Renungan_OpenAI_Service {

    private $api_key;
    private $config;
    private $logger;
    private $helper;

    public function __construct() {
        $this->config = new Elshadaifm_Renungan_Config();
        $this->logger = new Elshadaifm_Renungan_Logger();
        $this->helper = new Elshadaifm_Renungan_Helper();
        $this->api_key = $this->config->get_openai_key();
    }

    // API Key Management
    public function update_api_key($new_key) {
        $this->api_key = $new_key;
        $this->config->set_openai_key($new_key);
    }

    public function is_api_key_set() {
        return !empty($this->api_key);
    }

    // Content Data Extraction
    public function extract_content_data($content) {
        if (!$this->validate_api_key()) {
            return false;
        }

        $prompt = $this->build_extraction_prompt($content);
        $response = $this->call_openai_chat_api($prompt);

        if (!$response) {
            return false;
        }

        return $this->helper->parse_json_response($response);
    }

    // Renungan Content Generation
    public function generate_renungan_content($ayat, $judul, $summary) {
        if (!$this->validate_api_key()) {
            return false;
        }

        $style_guide = $this->load_style_guide();
        $prompt = $this->build_content_generation_prompt($ayat, $judul, $summary, $style_guide);
        $response = $this->call_openai_chat_api($prompt);

        if (!$response) {
            return false;
        }

        return $this->parse_text_response($response);
    }

    // Image Prompt Generation
    public function generate_image_prompt($ayat, $judul, $summary, $content) {
        if (!$this->validate_api_key()) {
            return false;
        }

        $prompt = $this->build_image_prompt_generation($ayat, $judul, $summary, $content);
        $response = $this->call_openai_chat_api($prompt);

        if (!$response) {
            return false;
        }

        return $this->parse_text_response($response);
    }

    // Image Generation
    public function generate_image_with_gpt_image($image_prompt) {
        if (!$this->validate_api_key()) {
            return false;
        }

        $response = $this->call_openai_image_api($image_prompt);

        if (!$response) {
            return false;
        }

        return $this->save_gpt_image_to_wordpress_media($response, $image_prompt);
    }

    // Connection Testing
    public function test_connection() {
        if (!$this->is_api_key_set()) {
            $this->logger->log('OpenAI API key not set', Elshadaifm_Renungan_Logger::WARNING);
            return array(
                'success' => false,
                'message' => 'API key not set'
            );
        }

        $this->logger->log_openai_call('test', 'connection');
        $response = $this->call_openai_chat_api('Hello, this is a test');

        if ($response && isset($response['choices'][0]['message']['content'])) {
            $this->logger->log('OpenAI connection test successful', Elshadaifm_Renungan_Logger::INFO);
            return array(
                'success' => true,
                'message' => 'Connection successful'
            );
        } else {
            $this->logger->log_error('OpenAI connection test failed');
            return array(
                'success' => false,
                'message' => 'Connection failed'
            );
        }
    }

    // Private Methods - API Communication
    private function validate_api_key() {
        if (empty($this->api_key)) {
            error_log('OpenAI API key not set');
            return false;
        }
        return true;
    }

    private function call_openai_chat_api($prompt) {
        $openai_config = $this->config->get_openai_config();
        $ch = curl_init();

        $request_data = array(
            'model' => $openai_config['model'],
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'temperature' => 0.3
        );

        curl_setopt_array($ch, array(
            CURLOPT_URL => $openai_config['chat_url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($request_data),
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $this->api_key,
                'Content-Type: application/json'
            ),
            CURLOPT_TIMEOUT => $openai_config['timeout'],
            CURLOPT_CONNECTTIMEOUT => $openai_config['connect_timeout'],
            CURLOPT_SSL_VERIFYPEER => false
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            $this->logger->log_error('cURL error: ' . $error);
            return false;
        }

        $this->logger->log_openai_call('chat', $openai_config['model']);

        $data = json_decode($response, true);

        if ($http_code !== 200) {
            $error_message = $data['error']['message'] ?? 'Unknown error';
            $this->logger->log_openai_error('chat', $error_message, $http_code);
            return false;
        }

        return $data;
    }

    private function call_openai_image_api($image_prompt) {
        $openai_config = $this->config->get_openai_config();
        $image_settings = $this->config->get_image_settings();

        $ch = curl_init();

        $request_data = array(
            'model' => $openai_config['image_model'],
            'prompt' => $image_prompt,
            'n' => 1,
            'size' => $image_settings['size']
        );

        curl_setopt_array($ch, array(
            CURLOPT_URL => $openai_config['image_url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($request_data),
            CURLOPT_HTTPHEADER => array(
                'Authorization: Bearer ' . $this->api_key,
                'Content-Type: application/json'
            ),
            CURLOPT_TIMEOUT => $openai_config['timeout'],
            CURLOPT_CONNECTTIMEOUT => $openai_config['connect_timeout'],
            CURLOPT_SSL_VERIFYPEER => false
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            $this->logger->log_error('cURL error: ' . $error);
            return false;
        }

        $this->logger->log_openai_call('image', $openai_config['image_model']);

        $data = json_decode($response, true);

        if ($http_code !== 200) {
            $error_message = $data['error']['message'] ?? 'Unknown error';
            $this->logger->log_openai_error('image', $error_message, $http_code);
            return false;
        }

        return $data;
    }

    // Private Methods - Prompt Building
    private function load_style_guide() {
        return $this->helper->load_style_guide();
    }

    private function build_extraction_prompt($content) {
        $style_guide = $this->load_style_guide();
        return "Using this language style guide, transform the renungan content into separate daily entries:

Language Style: $style_guide

Content to transform:
$content

Generate a JSON array where each object follows the  style:
- tanggal_publish: Date in YYYY-MM-DD format
- ayat: Bible verses for this date
- judul: Creative, metaphorical title using 'Otoritatif namun Peduli' tone
- summary: 100-word reflection following the 4-phase structure: Pembukaan → Pernyataan Inti → Isi (2-3 actionable points) → Penutup

Apply these style elements:
- Tone: Authoritative yet caring, motivational but warm
- Language: Simple, direct, personal address using 'Anda'/'Saudara'
- Structure: Opening question → Bold core statement → Practical numbered points → Hopeful closing
- Style: Use everyday analogies, repetition for emphasis, conversational approach

Output: JSON array with separate entries for each date, each following the message flow and tone guidelines.";
    }

    private function build_content_generation_prompt($ayat, $judul, $summary, $style_guide) {
        return "Using this language style guide, generate a complete renungan content:

Language Style: $style_guide

Input:
- Ayat: $ayat
- Judul: $judul
- Summary: $summary

Generate a complete renungan following the 'Gembala Inspiratif' style:

1. **Identify core spiritual theme** from the ayat and summary, then build content around that theme
2. **Include the ayat naturally** - read the full ayat within the content as God's word to the listener
3. **Use natural flowing structure** - don't force formulaic pembukaan/inti/isi/penutup
4. **Apply the style elements**: authoritative yet caring tone, simple language, personal address using 'Anda'/'Saudara', everyday analogies

Weave the ayat into the message naturally - either as foundation, reinforcement, or reminder. Make the ayat flow as part of the conversation, not separate from it.

Generate approximately 300-400 words. Return only the content text, no JSON formatting.";
    }

    private function build_image_prompt_generation($ayat, $judul, $summary, $content) {
        return "Generate a creative image prompt for Christian devotion based on:

Verse: $ayat
Title: $judul
Theme: $summary

**Required Elements:**
- Christian spiritual atmosphere
- Purple accent colors (#6B46C1, #9333EA)
- Modern minimalist style
- **NO text, letters, or words in the image**

**Creative Variables (mix 2-3):**
- Composition: [dramatic perspective, intimate close-up, wide landscape]
- Lighting: [soft morning light, golden hour, moody shadows]
- Elements: [subtle cross, light rays, abstract shapes, natural textures]
- Style: [photorealistic, painterly, digital art, cinematic]

**Important:** Focus on visual storytelling only - no text, no verses, no words in the generated image. Keep it under 100 words.";
    }

    // Private Methods - Response Parsing
    private function parse_json_response($data) {
        if (!isset($data['choices'][0]['message']['content'])) {
            $this->logger->log_error('No content in OpenAI response');
            return false;
        }

        $content_response = $data['choices'][0]['message']['content'];
        $this->logger->log_debug('OpenAI Content Response: ' . $content_response);

        // Parse JSON response using helper
        return $this->helper->parse_json_response($content_response);
    }

    private function parse_text_response($data) {
        if (!isset($data['choices'][0]['message']['content'])) {
            $this->logger->log_error('No content in OpenAI response');
            return false;
        }

        $content = $data['choices'][0]['message']['content'];
        $this->logger->log_debug('OpenAI Generated Content: ' . $content);

        return $this->helper->clean_markdown($content, 'text');
    }

    // Private Methods - WordPress Media Integration
    private function save_gpt_image_to_wordpress_media($data, $image_prompt) {
        if (!isset($data['data'][0]['b64_json'])) {
            $this->logger->log_error('No image data in GPT Image response');
            return false;
        }

        $image_data = $data['data'][0]['b64_json'];

        // Convert base64 to image file
        $upload_dir = $this->helper->ensure_upload_directory();
        $filename = $this->helper->generate_filename('renungan_', 'jpg');
        $filepath = $upload_dir['path'] . '/' . $filename;

        // Decode base64 and save to file
        $image_binary = base64_decode($image_data);
        if (file_put_contents($filepath, $image_binary) === false) {
            $this->logger->log_error('Failed to save image file');
            return false;
        }

        // Prepare file array for WordPress media handle
        $file_array = array(
            'name' => $filename,
            'type' => 'image/jpeg',
            'tmp_name' => $filepath,
            'error' => 0,
            'size' => filesize($filepath)
        );

        // Include WordPress file handling functions
        $this->load_wordpress_media_functions();

        // Upload to WordPress media library
        $attachment_id = media_handle_sideload($file_array, 0);

        // Check for errors
        if (is_wp_error($attachment_id)) {
            $this->logger->log_error('Media upload error: ' . $attachment_id->get_error_message());
            // Clean up temporary file
            @unlink($filepath);
            return false;
        }

        // Set attachment metadata
        $this->set_attachment_metadata($attachment_id, $image_prompt);

        // Clean up temporary file
        @unlink($filepath);

        return $attachment_id;
    }

    private function load_wordpress_media_functions() {
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        require_once(ABSPATH . 'wp-admin/includes/image.php');
    }

    private function set_attachment_metadata($attachment_id, $image_prompt) {
        $attachment_data = array(
            'ID' => $attachment_id,
            'post_title' => 'Renungan Image - ' . date('Y-m-d'),
            'post_excerpt' => $image_prompt,
            'post_content' => $image_prompt,
            'post_status' => 'inherit'
        );

        wp_update_post($attachment_data);
        update_post_meta($attachment_id, '_wp_attachment_image_alt', $image_prompt);
    }
}